# Hướng dẫn sử dụng Bank Settings với Filament Spatie Settings

## Tổng quan

Hệ thống Bank Settings được xây dựng sử dụng package `filament/spatie-laravel-settings-plugin` để quản lý thông tin kết nối và access token của các ngân hàng (OCB, MB Bank, KLB Bank).

## Cấu trúc

### 1. BankSettings Class
- **File**: `app/Settings/BankSettings.php`
- **Chức năng**: Quản lý cấu hình cho tất cả ngân hàng
- **<PERSON><PERSON><PERSON> thuộc tính**:
  - `{bank}_access_token`: Access token hiện tại
  - `{bank}_token_expires_at`: Thời gian hết hạn token
  - `{bank}_refresh_token`: Refresh token
  - `{bank}_client_id`: Client ID
  - `{bank}_client_secret`: Client Secret
  - `{bank}_api_url`: URL API của ngân hàng

### 2. BankAuthService
- **File**: `modules/Bank/app/Services/BankAuthService.php`
- **Ch<PERSON><PERSON> năng**: Xử lý authentication và quản lý token
- **Các method chính**:
  - `getAccessToken()`: Lấy token hợp lệ
  - `refreshAccessToken()`: Refresh token khi hết hạn
  - `authenticate()`: Xác thực với ngân hàng
  - `makeAuthenticatedRequest()`: Thực hiện API call có xác thực

### 3. Filament Settings Page
- **File**: `app/Filament/Pages/BankSettingsPage.php`
- **Chức năng**: Giao diện quản lý cấu hình ngân hàng trong Filament Admin

## Cài đặt và sử dụng

### 1. Khởi tạo settings
```bash
php artisan bank:init-settings
```

### 2. Cấu hình thông tin ngân hàng
1. Truy cập Filament Admin Panel
2. Vào mục "Cấu hình hệ thống" > "Cấu hình ngân hàng"
3. Nhập thông tin cho từng ngân hàng:
   - Client ID
   - Client Secret
   - API URL (đã có sẵn mặc định)

### 3. Test kết nối
```bash
# Test tất cả ngân hàng
php artisan bank:test-connection

# Test ngân hàng cụ thể
php artisan bank:test-connection ocb
php artisan bank:test-connection mb
php artisan bank:test-connection klb
```

## Sử dụng trong code

### 1. Lấy access token
```php
use Modules\Bank\Services\BankAuthService;

$authService = new BankAuthService();
$token = $authService->getAccessToken('ocb');
```

### 2. Thực hiện API call có xác thực
```php
$result = $authService->makeAuthenticatedRequest('ocb', 'POST', '/api/endpoint', [
    'data' => 'value'
]);

if ($result['success']) {
    $responseData = $result['data'];
    // Xử lý dữ liệu
} else {
    // Xử lý lỗi
    $error = $result['message'];
}
```

### 3. Sử dụng trong Strategy
```php
class OcbStrategy implements BankStrategyInterface
{
    protected BankAuthService $authService;

    public function __construct(array $config = [])
    {
        $this->authService = new BankAuthService();
    }

    public function checkAccountNumber(string $accountNumber, array $additionalData = []): BankAccountValidationResult
    {
        $result = $this->authService->makeAuthenticatedRequest('ocb', 'POST', '/verify-account', [
            'account_number' => $accountNumber,
            // ... other data
        ]);

        // Xử lý kết quả
    }
}
```

## Quản lý token tự động

Hệ thống tự động:
1. Kiểm tra token có hết hạn không
2. Refresh token khi cần thiết
3. Retry API call nếu token hết hạn
4. Log tất cả hoạt động authentication

## Commands hữu ích

```bash
# Khởi tạo settings mặc định
php artisan bank:init-settings

# Test kết nối tất cả ngân hàng
php artisan bank:test-connection

# Test kết nối ngân hàng cụ thể
php artisan bank:test-connection ocb

# Xem migration
php artisan migrate:status

# Rollback settings (nếu cần)
php artisan migrate:rollback --step=1
```

## Lưu ý bảo mật

1. **Client Secret**: Được mã hóa và chỉ hiển thị dạng password trong form
2. **Access Token**: Chỉ đọc trong giao diện, không thể chỉnh sửa trực tiếp
3. **Refresh Token**: Được quản lý tự động, không hiển thị trong giao diện
4. **Logging**: Tất cả hoạt động được log để audit

## Troubleshooting

### Token không refresh được
1. Kiểm tra Client ID/Secret có đúng không
2. Kiểm tra API URL có accessible không
3. Xem log trong `storage/logs/laravel.log`

### API call thất bại
1. Kiểm tra token có hợp lệ không: `php artisan bank:test-connection`
2. Kiểm tra endpoint API có đúng không
3. Kiểm tra network connectivity

### Settings không lưu được
1. Kiểm tra bảng `settings` đã được tạo chưa
2. Chạy lại migration: `php artisan migrate`
3. Kiểm tra quyền ghi database
