# Bank Settings với Environment Variables

## Tổng quan

Hệ thống Bank Settings đã được cập nhật để sử dụng environment variables cho thông tin nhạy cảm và chỉ lưu token trong database settings.

## Cấu trúc mới

### 1. Environment Variables (.env)

Thông tin nhạy cảm được lưu trong file `.env`:

```bash
# OCB Bank
OCB_USERNAME=your_ocb_username
OCB_PASSWORD=your_ocb_password
OCB_CLIENT_ID=your_ocb_client_id
OCB_CLIENT_SECRET=your_ocb_client_secret
OCB_API_URL=https://api.ocb.com.vn

# MB Bank
MB_USERNAME=your_mb_username
MB_PASSWORD=your_mb_password
MB_API_URL=https://api.mbbank.com.vn

# KLB Bank
KLB_CLIENT_ID=your_klb_client_id
KLB_CLIENT_SECRET=your_klb_client_secret
KLB_CLIENT_ENCRYPT_KEY=your_klb_encrypt_key
KLB_API_URL=https://api.klb.com.vn
```

### 2. Database Settings

Chỉ lưu token information:

- `{bank}_access_token`
- `{bank}_token_expires_at`
- `{bank}_refresh_token`

### 3. Config File

`modules/Bank/config/config.php` đọc từ environment variables.

## Cài đặt và sử dụng

### 1. Cấu hình environment variables

```bash
# Xem hướng dẫn cấu hình
php artisan bank:env-guide

# Chỉnh sửa .env và thêm thông tin ngân hàng thực
nano .env

# Ví dụ cấu hình trong .env:
OCB_USERNAME=your_actual_username
OCB_PASSWORD=your_actual_password
OCB_CLIENT_ID=your_actual_client_id
OCB_CLIENT_SECRET=your_actual_client_secret
# ... tương tự cho MB và KLB
```

### 2. Cập nhật settings structure

```bash
php artisan bank:update-settings
```

### 3. Kiểm tra cấu hình

```bash
php artisan bank:init-settings
```

### 4. Test kết nối

```bash
# Test tất cả ngân hàng
php artisan bank:test-connection

# Test ngân hàng cụ thể
php artisan bank:test-connection ocb
```

## Sử dụng trong code

### 1. Lấy credentials từ config

```php
$credentials = config('bank.credentials.ocb');
// Returns: ['username' => '...', 'password' => '...', 'client_id' => '...', ...]
```

### 2. Sử dụng BankAuthService

```php
use Modules\Bank\Services\BankAuthService;

$authService = new BankAuthService();
$token = $authService->getAccessToken('ocb');

$result = $authService->makeAuthenticatedRequest('ocb', 'POST', '/api/endpoint', [
  'data' => 'value',
]);
```

### 3. Lấy token từ settings

```php
use App\Settings\BankSettings;

$settings = app(BankSettings::class);
$token = $settings->getAccessToken('ocb');
$isExpired = $settings->isTokenExpired('ocb');
```

## Bảo mật

### ✅ Ưu điểm

- Credentials không lưu trong database
- Không commit thông tin nhạy cảm vào git
- Dễ dàng thay đổi credentials qua environment
- Token được quản lý tự động

### 🔒 Lưu ý bảo mật

- Không commit file `.env` vào git
- Sử dụng `.env.example` để hướng dẫn
- Backup credentials an toàn
- Rotate credentials định kỳ

## Commands

```bash
# Xem hướng dẫn cấu hình environment variables
php artisan bank:env-guide

# Cập nhật settings structure
php artisan bank:update-settings

# Kiểm tra cấu hình
php artisan bank:init-settings

# Test kết nối
php artisan bank:test-connection [bank_code]

# Clear config cache sau khi thay đổi .env
php artisan config:clear
```

## Troubleshooting

### Missing environment variables

```bash
# Kiểm tra file .env có đầy đủ variables không
php artisan bank:init-settings
```

### Token issues

```bash
# Xem token status
php artisan bank:test-connection
```

### Config cache

```bash
# Clear config cache sau khi thay đổi .env
php artisan config:clear
```
