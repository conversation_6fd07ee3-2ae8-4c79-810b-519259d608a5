<?php

namespace App\Settings;

use <PERSON><PERSON>\LaravelSettings\Settings;

class BankSettings extends Settings
{
    // OCB Bank Settings
    public ?string $ocb_username;
    public ?string $ocb_password;
    public ?string $ocb_client_id;
    public ?string $ocb_client_secret;
    public ?string $ocb_access_token;
    public ?string $ocb_token_expires_at;
    public ?string $ocb_refresh_token;

    // MB Bank Settings
    public ?string $mb_username;
    public ?string $mb_password;
    public ?string $mb_access_token;
    public ?string $mb_token_expires_at;
    public ?string $mb_refresh_token;

    // KLB Bank Settings
    public ?string $klb_client_id;
    public ?string $klb_client_secret;
    public ?string $klb_client_encrypt_key;
    public ?string $klb_access_token;
    public ?string $klb_token_expires_at;
    public ?string $klb_refresh_token;

    public static function group(): string
    {
        return 'bank';
    }

    /**
     * Get access token for specific bank
     */
    public function getAccessToken(string $bankCode): ?string
    {
        $property = strtolower($bankCode) . '_access_token';
        return $this->{$property} ?? null;
    }

    /**
     * Set access token for specific bank
     */
    public function setAccessToken(string $bankCode, string $token, ?string $expiresAt = null): void
    {
        $tokenProperty = strtolower($bankCode) . '_access_token';
        $expiresProperty = strtolower($bankCode) . '_token_expires_at';

        $this->{$tokenProperty} = $token;
        if ($expiresAt) {
            $this->{$expiresProperty} = $expiresAt;
        }

        $this->save();
    }

    /**
     * Get token expiration for specific bank
     */
    public function getTokenExpiration(string $bankCode): ?string
    {
        $property = strtolower($bankCode) . '_token_expires_at';
        return $this->{$property} ?? null;
    }

    /**
     * Check if token is expired for specific bank
     */
    public function isTokenExpired(string $bankCode): bool
    {
        $expiresAt = $this->getTokenExpiration($bankCode);
        if (!$expiresAt) {
            return true;
        }

        return now()->isAfter($expiresAt);
    }

    /**
     * Get refresh token for specific bank
     */
    public function getRefreshToken(string $bankCode): ?string
    {
        $property = strtolower($bankCode) . '_refresh_token';
        return $this->{$property} ?? null;
    }

    /**
     * Set refresh token for specific bank
     */
    public function setRefreshToken(string $bankCode, string $refreshToken): void
    {
        $property = strtolower($bankCode) . '_refresh_token';
        $this->{$property} = $refreshToken;
        $this->save();
    }

    /**
     * Get username for specific bank
     */
    public function getUsername(string $bankCode): ?string
    {
        $property = strtolower($bankCode) . '_username';
        return $this->{$property} ?? null;
    }

    /**
     * Set username for specific bank
     */
    public function setUsername(string $bankCode, string $username): void
    {
        $property = strtolower($bankCode) . '_username';
        $this->{$property} = $username;
        $this->save();
    }

    /**
     * Get password for specific bank
     */
    public function getPassword(string $bankCode): ?string
    {
        $property = strtolower($bankCode) . '_password';
        return $this->{$property} ?? null;
    }

    /**
     * Set password for specific bank
     */
    public function setPassword(string $bankCode, string $password): void
    {
        $property = strtolower($bankCode) . '_password';
        $this->{$property} = $password;
        $this->save();
    }

    /**
     * Get client credentials for specific bank
     */
    public function getClientCredentials(string $bankCode): array
    {
        $clientIdProperty = strtolower($bankCode) . '_client_id';
        $clientSecretProperty = strtolower($bankCode) . '_client_secret';

        return [
            'client_id' => $this->{$clientIdProperty} ?? null,
            'client_secret' => $this->{$clientSecretProperty} ?? null,
        ];
    }

    /**
     * Set client credentials for specific bank
     */
    public function setClientCredentials(string $bankCode, string $clientId, string $clientSecret): void
    {
        $clientIdProperty = strtolower($bankCode) . '_client_id';
        $clientSecretProperty = strtolower($bankCode) . '_client_secret';

        $this->{$clientIdProperty} = $clientId;
        $this->{$clientSecretProperty} = $clientSecret;
        $this->save();
    }

    /**
     * Get encrypt key for specific bank (KLB only)
     */
    public function getEncryptKey(string $bankCode): ?string
    {
        $property = strtolower($bankCode) . '_client_encrypt_key';
        return $this->{$property} ?? null;
    }

    /**
     * Set encrypt key for specific bank (KLB only)
     */
    public function setEncryptKey(string $bankCode, string $encryptKey): void
    {
        $property = strtolower($bankCode) . '_client_encrypt_key';
        $this->{$property} = $encryptKey;
        $this->save();
    }

    /**
     * Get user credentials for specific bank
     */
    public function getUserCredentials(string $bankCode): array
    {
        return [
            'username' => $this->getUsername($bankCode),
            'password' => $this->getPassword($bankCode),
        ];
    }

    /**
     * Clear all tokens for specific bank
     */
    public function clearTokens(string $bankCode): void
    {
        $tokenProperty = strtolower($bankCode) . '_access_token';
        $expiresProperty = strtolower($bankCode) . '_token_expires_at';
        $refreshProperty = strtolower($bankCode) . '_refresh_token';

        $this->{$tokenProperty} = null;
        $this->{$expiresProperty} = null;
        $this->{$refreshProperty} = null;
        $this->save();
    }

    /**
     * Get all bank configurations
     */
    public function getAllBankConfigs(): array
    {
        $banks = ['ocb', 'mb', 'klb'];
        $configs = [];

        foreach ($banks as $bank) {
            $config = [
                'access_token' => $this->getAccessToken($bank),
                'token_expires_at' => $this->getTokenExpiration($bank),
                'refresh_token' => $this->getRefreshToken($bank),
                'is_token_expired' => $this->isTokenExpired($bank),
            ];

            // Add bank-specific configurations
            if ($bank === 'ocb') {
                $config['username'] = $this->getUsername($bank);
                $config['password'] = $this->getPassword($bank);
                $config['client_credentials'] = $this->getClientCredentials($bank);
            } elseif ($bank === 'mb') {
                $config['username'] = $this->getUsername($bank);
                $config['password'] = $this->getPassword($bank);
            } elseif ($bank === 'klb') {
                $config['client_credentials'] = $this->getClientCredentials($bank);
                $config['encrypt_key'] = $this->getEncryptKey($bank);
            }

            $configs[$bank] = $config;
        }

        return $configs;
    }

    /**
     * Initialize default settings for all banks
     */
    public function initializeDefaults(): void
    {
        // No default initialization needed for new structure
        // All fields start as null and are configured via admin panel
    }
}
