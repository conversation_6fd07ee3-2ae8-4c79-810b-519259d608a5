<?php

namespace App\Settings;

use <PERSON><PERSON>\LaravelSettings\Settings;

class BankSettings extends Settings
{
    // OCB Bank Settings
    public ?string $ocb_access_token;
    public ?string $ocb_token_expires_at;
    public ?string $ocb_refresh_token;
    public ?string $ocb_client_id;
    public ?string $ocb_client_secret;
    public ?string $ocb_api_url;

    // MB Bank Settings
    public ?string $mb_access_token;
    public ?string $mb_token_expires_at;
    public ?string $mb_refresh_token;
    public ?string $mb_client_id;
    public ?string $mb_client_secret;
    public ?string $mb_api_url;

    // KLB Bank Settings
    public ?string $klb_access_token;
    public ?string $klb_token_expires_at;
    public ?string $klb_refresh_token;
    public ?string $klb_client_id;
    public ?string $klb_client_secret;
    public ?string $klb_api_url;

    public static function group(): string
    {
        return 'bank';
    }

    /**
     * Get access token for specific bank
     */
    public function getAccessToken(string $bankCode): ?string
    {
        $property = strtolower($bankCode) . '_access_token';
        return $this->{$property} ?? null;
    }

    /**
     * Set access token for specific bank
     */
    public function setAccessToken(string $bankCode, string $token, ?string $expiresAt = null): void
    {
        $tokenProperty = strtolower($bankCode) . '_access_token';
        $expiresProperty = strtolower($bankCode) . '_token_expires_at';

        $this->{$tokenProperty} = $token;
        if ($expiresAt) {
            $this->{$expiresProperty} = $expiresAt;
        }

        $this->save();
    }

    /**
     * Get token expiration for specific bank
     */
    public function getTokenExpiration(string $bankCode): ?string
    {
        $property = strtolower($bankCode) . '_token_expires_at';
        return $this->{$property} ?? null;
    }

    /**
     * Check if token is expired for specific bank
     */
    public function isTokenExpired(string $bankCode): bool
    {
        $expiresAt = $this->getTokenExpiration($bankCode);
        if (!$expiresAt) {
            return true;
        }

        return now()->isAfter($expiresAt);
    }

    /**
     * Get refresh token for specific bank
     */
    public function getRefreshToken(string $bankCode): ?string
    {
        $property = strtolower($bankCode) . '_refresh_token';
        return $this->{$property} ?? null;
    }

    /**
     * Set refresh token for specific bank
     */
    public function setRefreshToken(string $bankCode, string $refreshToken): void
    {
        $property = strtolower($bankCode) . '_refresh_token';
        $this->{$property} = $refreshToken;
        $this->save();
    }

    /**
     * Get client credentials for specific bank
     */
    public function getClientCredentials(string $bankCode): array
    {
        $clientIdProperty = strtolower($bankCode) . '_client_id';
        $clientSecretProperty = strtolower($bankCode) . '_client_secret';

        return [
            'client_id' => $this->{$clientIdProperty} ?? null,
            'client_secret' => $this->{$clientSecretProperty} ?? null,
        ];
    }

    /**
     * Set client credentials for specific bank
     */
    public function setClientCredentials(string $bankCode, string $clientId, string $clientSecret): void
    {
        $clientIdProperty = strtolower($bankCode) . '_client_id';
        $clientSecretProperty = strtolower($bankCode) . '_client_secret';

        $this->{$clientIdProperty} = $clientId;
        $this->{$clientSecretProperty} = $clientSecret;
        $this->save();
    }

    /**
     * Get API URL for specific bank
     */
    public function getApiUrl(string $bankCode): ?string
    {
        $property = strtolower($bankCode) . '_api_url';
        return $this->{$property} ?? null;
    }

    /**
     * Set API URL for specific bank
     */
    public function setApiUrl(string $bankCode, string $apiUrl): void
    {
        $property = strtolower($bankCode) . '_api_url';
        $this->{$property} = $apiUrl;
        $this->save();
    }

    /**
     * Clear all tokens for specific bank
     */
    public function clearTokens(string $bankCode): void
    {
        $tokenProperty = strtolower($bankCode) . '_access_token';
        $expiresProperty = strtolower($bankCode) . '_token_expires_at';
        $refreshProperty = strtolower($bankCode) . '_refresh_token';

        $this->{$tokenProperty} = null;
        $this->{$expiresProperty} = null;
        $this->{$refreshProperty} = null;
        $this->save();
    }

    /**
     * Get all bank configurations
     */
    public function getAllBankConfigs(): array
    {
        $banks = ['ocb', 'mb', 'klb'];
        $configs = [];

        foreach ($banks as $bank) {
            $configs[$bank] = [
                'access_token' => $this->getAccessToken($bank),
                'token_expires_at' => $this->getTokenExpiration($bank),
                'refresh_token' => $this->getRefreshToken($bank),
                'client_credentials' => $this->getClientCredentials($bank),
                'api_url' => $this->getApiUrl($bank),
                'is_token_expired' => $this->isTokenExpired($bank),
            ];
        }

        return $configs;
    }

    /**
     * Initialize default settings for all banks
     */
    public function initializeDefaults(): void
    {
        $defaults = [
            'ocb' => [
                'api_url' => 'https://api.ocb.com.vn',
            ],
            'mb' => [
                'api_url' => 'https://api.mbbank.com.vn',
            ],
            'klb' => [
                'api_url' => 'https://api.klb.com.vn',
            ],
        ];

        foreach ($defaults as $bankCode => $config) {
            if (!$this->getApiUrl($bankCode)) {
                $this->setApiUrl($bankCode, $config['api_url']);
            }
        }
    }
}
