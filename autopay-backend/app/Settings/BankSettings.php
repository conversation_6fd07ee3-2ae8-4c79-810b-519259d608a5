<?php

namespace App\Settings;

use <PERSON><PERSON>\LaravelSettings\Settings;

class BankSettings extends Settings
{
    // OCB Bank Token Settings
    public ?string $ocb_access_token;
    public ?string $ocb_token_expires_at;
    public ?string $ocb_refresh_token;

    // MB Bank Token Settings
    public ?string $mb_access_token;
    public ?string $mb_token_expires_at;
    public ?string $mb_refresh_token;

    // KLB Bank Token Settings
    public ?string $klb_access_token;
    public ?string $klb_token_expires_at;
    public ?string $klb_refresh_token;

    public static function group(): string
    {
        return 'bank';
    }

    /**
     * Get access token for specific bank
     */
    public function getAccessToken(string $bankCode): ?string
    {
        $property = strtolower($bankCode) . '_access_token';
        return $this->{$property} ?? null;
    }

    /**
     * Set access token for specific bank
     */
    public function setAccessToken(string $bankCode, string $token, ?string $expiresAt = null): void
    {
        $tokenProperty = strtolower($bankCode) . '_access_token';
        $expiresProperty = strtolower($bankCode) . '_token_expires_at';

        $this->{$tokenProperty} = $token;
        if ($expiresAt) {
            $this->{$expiresProperty} = $expiresAt;
        }

        $this->save();
    }

    /**
     * Get token expiration for specific bank
     */
    public function getTokenExpiration(string $bankCode): ?string
    {
        $property = strtolower($bankCode) . '_token_expires_at';
        return $this->{$property} ?? null;
    }

    /**
     * Check if token is expired for specific bank
     */
    public function isTokenExpired(string $bankCode): bool
    {
        $expiresAt = $this->getTokenExpiration($bankCode);
        if (!$expiresAt) {
            return true;
        }

        return now()->isAfter($expiresAt);
    }

    /**
     * Get refresh token for specific bank
     */
    public function getRefreshToken(string $bankCode): ?string
    {
        $property = strtolower($bankCode) . '_refresh_token';
        return $this->{$property} ?? null;
    }

    /**
     * Set refresh token for specific bank
     */
    public function setRefreshToken(string $bankCode, string $refreshToken): void
    {
        $property = strtolower($bankCode) . '_refresh_token';
        $this->{$property} = $refreshToken;
        $this->save();
    }

    /**
     * Get bank credentials from config
     */
    public function getBankCredentials(string $bankCode): array
    {
        return config("bank.credentials.{$bankCode}", []);
    }

    /**
     * Clear all tokens for specific bank
     */
    public function clearTokens(string $bankCode): void
    {
        $tokenProperty = strtolower($bankCode) . '_access_token';
        $expiresProperty = strtolower($bankCode) . '_token_expires_at';
        $refreshProperty = strtolower($bankCode) . '_refresh_token';

        $this->{$tokenProperty} = null;
        $this->{$expiresProperty} = null;
        $this->{$refreshProperty} = null;
        $this->save();
    }

    /**
     * Get all bank configurations
     */
    public function getAllBankConfigs(): array
    {
        $banks = ['ocb', 'mb', 'klb'];
        $configs = [];

        foreach ($banks as $bank) {
            $credentials = $this->getBankCredentials($bank);

            $config = [
                'access_token' => $this->getAccessToken($bank),
                'token_expires_at' => $this->getTokenExpiration($bank),
                'refresh_token' => $this->getRefreshToken($bank),
                'is_token_expired' => $this->isTokenExpired($bank),
                'credentials' => $credentials,
                'has_credentials' => !empty(array_filter($credentials)),
            ];

            $configs[$bank] = $config;
        }

        return $configs;
    }

    /**
     * Initialize default settings for all banks
     */
    public function initializeDefaults(): void
    {
        // No default initialization needed for new structure
        // All fields start as null and are configured via admin panel
    }
}
