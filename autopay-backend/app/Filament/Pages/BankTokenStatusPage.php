<?php

namespace App\Filament\Pages;

use App\Settings\BankSettings;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;
use Filament\Support\Exceptions\Halt;
use Filament\Notifications\Notification;
use Filament\Actions\Action;
use Modules\Bank\Services\BankAuthService;

class BankTokenStatusPage extends SettingsPage
{
    protected static ?string $navigationIcon = 'heroicon-o-building-library';

    protected static string $settings = BankSettings::class;

    protected static ?string $navigationGroup = 'Cấu hình hệ thống';

    protected static ?string $title = 'Trạng thái Token Ngân hàng';

    protected static ?string $navigationLabel = 'Token Ngân hàng';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin cấu hình')
                    ->description('Credentials được cấu hình trong file .env. Chỉ token information được lưu trong database.')
                    ->schema([
                        Forms\Components\Placeholder::make('env_info')
                            ->label('')
                            ->content('💡 Để cấu hình credentials, vui lòng chỉnh sửa file .env với các biến: OCB_USERNAME, OCB_PASSWORD, OCB_CLIENT_ID, OCB_CLIENT_SECRET, MB_USERNAME, MB_PASSWORD, KLB_CLIENT_ID, KLB_CLIENT_SECRET, KLB_CLIENT_ENCRYPT_KEY'),
                    ]),

                Forms\Components\Tabs::make('Bank Token Status')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('OCB Bank')
                            ->icon('heroicon-o-building-library')
                            ->schema([
                                Forms\Components\Section::make('Thông tin cấu hình OCB')
                                    ->description('Trạng thái cấu hình từ environment variables')
                                    ->schema([
                                        Forms\Components\Placeholder::make('ocb_username_status')
                                            ->label('Username')
                                            ->content(fn() => config('bank.credentials.ocb.username') ? '✅ Đã cấu hình' : '❌ Chưa cấu hình (OCB_USERNAME)'),
                                        Forms\Components\Placeholder::make('ocb_password_status')
                                            ->label('Password')
                                            ->content(fn() => config('bank.credentials.ocb.password') ? '✅ Đã cấu hình' : '❌ Chưa cấu hình (OCB_PASSWORD)'),
                                        Forms\Components\Placeholder::make('ocb_client_id_status')
                                            ->label('Client ID')
                                            ->content(fn() => config('bank.credentials.ocb.client_id') ? '✅ Đã cấu hình' : '❌ Chưa cấu hình (OCB_CLIENT_ID)'),
                                        Forms\Components\Placeholder::make('ocb_client_secret_status')
                                            ->label('Client Secret')
                                            ->content(fn() => config('bank.credentials.ocb.client_secret') ? '✅ Đã cấu hình' : '❌ Chưa cấu hình (OCB_CLIENT_SECRET)'),
                                    ])
                                    ->columns(2),
                                Forms\Components\Section::make('Token thông tin OCB')
                                    ->description('Thông tin token hiện tại (chỉ đọc)')
                                    ->schema([
                                        Forms\Components\TextInput::make('ocb_access_token')
                                            ->label('Access Token')
                                            ->disabled()
                                            ->dehydrated(false)
                                            ->placeholder('Chưa có token'),
                                        Forms\Components\TextInput::make('ocb_refresh_token')
                                            ->label('Refresh Token')
                                            ->disabled()
                                            ->dehydrated(false)
                                            ->placeholder('Chưa có refresh token'),
                                        Forms\Components\DateTimePicker::make('ocb_token_expires_at')
                                            ->label('Token hết hạn')
                                            ->disabled()
                                            ->dehydrated(false),
                                        Forms\Components\DateTimePicker::make('ocb_token_requested_at')
                                            ->label('Token được request')
                                            ->disabled()
                                            ->dehydrated(false),
                                    ])
                                    ->columns(2),
                            ]),
                        Forms\Components\Tabs\Tab::make('MB Bank')
                            ->icon('heroicon-o-building-library')
                            ->schema([
                                Forms\Components\Section::make('Thông tin cấu hình MB Bank')
                                    ->description('Trạng thái cấu hình từ environment variables')
                                    ->schema([
                                        Forms\Components\Placeholder::make('mb_username_status')
                                            ->label('Username')
                                            ->content(fn() => config('bank.credentials.mb.username') ? '✅ Đã cấu hình' : '❌ Chưa cấu hình (MB_USERNAME)'),
                                        Forms\Components\Placeholder::make('mb_password_status')
                                            ->label('Password')
                                            ->content(fn() => config('bank.credentials.mb.password') ? '✅ Đã cấu hình' : '❌ Chưa cấu hình (MB_PASSWORD)'),
                                    ])
                                    ->columns(2),
                                Forms\Components\Section::make('Token thông tin MB Bank')
                                    ->description('Thông tin token hiện tại (chỉ đọc)')
                                    ->schema([
                                        Forms\Components\TextInput::make('mb_access_token')
                                            ->label('Access Token')
                                            ->disabled()
                                            ->dehydrated(false)
                                            ->placeholder('Chưa có token'),
                                        Forms\Components\TextInput::make('mb_refresh_token')
                                            ->label('Refresh Token')
                                            ->disabled()
                                            ->dehydrated(false)
                                            ->placeholder('Chưa có refresh token'),
                                        Forms\Components\DateTimePicker::make('mb_token_expires_at')
                                            ->label('Token hết hạn')
                                            ->disabled()
                                            ->dehydrated(false),
                                        Forms\Components\DateTimePicker::make('mb_token_requested_at')
                                            ->label('Token được request')
                                            ->disabled()
                                            ->dehydrated(false),
                                    ])
                                    ->columns(2),
                            ]),
                        Forms\Components\Tabs\Tab::make('KLB Bank')
                            ->icon('heroicon-o-building-library')
                            ->schema([
                                Forms\Components\Section::make('Thông tin cấu hình KLB Bank')
                                    ->description('Trạng thái cấu hình từ environment variables')
                                    ->schema([
                                        Forms\Components\Placeholder::make('klb_client_id_status')
                                            ->label('Client ID')
                                            ->content(fn() => config('bank.credentials.klb.client_id') ? '✅ Đã cấu hình' : '❌ Chưa cấu hình (KLB_CLIENT_ID)'),
                                        Forms\Components\Placeholder::make('klb_client_secret_status')
                                            ->label('Client Secret')
                                            ->content(fn() => config('bank.credentials.klb.client_secret') ? '✅ Đã cấu hình' : '❌ Chưa cấu hình (KLB_CLIENT_SECRET)'),
                                        Forms\Components\Placeholder::make('klb_encrypt_key_status')
                                            ->label('Encrypt Key')
                                            ->content(fn() => config('bank.credentials.klb.client_encrypt_key') ? '✅ Đã cấu hình' : '❌ Chưa cấu hình (KLB_CLIENT_ENCRYPT_KEY)'),
                                    ])
                                    ->columns(2),
                                Forms\Components\Section::make('Token thông tin KLB Bank')
                                    ->description('Thông tin token hiện tại (chỉ đọc)')
                                    ->schema([
                                        Forms\Components\TextInput::make('klb_access_token')
                                            ->label('Access Token')
                                            ->disabled()
                                            ->dehydrated(false)
                                            ->placeholder('Chưa có token'),
                                        Forms\Components\TextInput::make('klb_refresh_token')
                                            ->label('Refresh Token')
                                            ->disabled()
                                            ->dehydrated(false)
                                            ->placeholder('Chưa có refresh token'),
                                        Forms\Components\DateTimePicker::make('klb_token_expires_at')
                                            ->label('Token hết hạn')
                                            ->disabled()
                                            ->dehydrated(false),
                                        Forms\Components\DateTimePicker::make('klb_token_requested_at')
                                            ->label('Token được request')
                                            ->disabled()
                                            ->dehydrated(false),
                                    ])
                                    ->columns(2),
                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('test_all_connections')
                ->label('Test tất cả kết nối')
                ->icon('heroicon-o-signal')
                ->color('primary')
                ->action(function () {
                    $authService = new BankAuthService();
                    $banks = ['ocb', 'mb', 'klb'];
                    $results = [];

                    foreach ($banks as $bank) {
                        $credentials = config("bank.credentials.{$bank}");
                        $missingCredentials = array_filter($credentials, fn($value) => empty($value));

                        if (!empty($missingCredentials)) {
                            $results[] = strtoupper($bank) . ': ❌ Missing credentials';
                        } else {
                            $results[] = strtoupper($bank) . ': ✅ Credentials configured';
                        }
                    }

                    Notification::make()
                        ->title('Kết quả test kết nối')
                        ->body(implode('<br>', $results))
                        ->success()
                        ->send();
                }),
            Action::make('refresh_page')
                ->label('Làm mới')
                ->icon('heroicon-o-arrow-path')
                ->color('gray')
                ->action(function () {
                    $this->redirect(request()->header('Referer'));
                }),
        ];
    }

    protected function beforeSave(): void
    {
        // Prevent saving since this is a read-only page
        Notification::make()
            ->title('Thông báo')
            ->body('Trang này chỉ để xem trạng thái token. Để cấu hình credentials, vui lòng chỉnh sửa file .env')
            ->warning()
            ->send();

        throw new Halt();
    }
}
