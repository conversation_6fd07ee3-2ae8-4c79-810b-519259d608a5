<?php

namespace App\Filament\Pages;

use App\Settings\BankSettings;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;

class BankSettingsPage extends SettingsPage
{
    protected static ?string $navigationIcon = 'heroicon-o-building-library';

    protected static string $settings = BankSettings::class;

    protected static ?string $navigationGroup = 'Cấu hình hệ thống';

    protected static ?string $title = 'Cấu hình ngân hàng';

    protected static ?string $navigationLabel = 'Cấu hình ngân hàng';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make('Bank Settings')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('OCB Bank')
                            ->icon('heroicon-o-building-library')
                            ->schema([
                                Forms\Components\Section::make('Thông tin đăng nhập OCB')
                                    ->description('Cấu hình thông tin đăng nhập với ngân hàng OCB')
                                    ->schema([
                                        Forms\Components\TextInput::make('ocb_username')
                                            ->label('Username')
                                            ->required()
                                            ->maxLength(255),
                                        Forms\Components\TextInput::make('ocb_password')
                                            ->label('Password')
                                            ->password()
                                            ->required()
                                            ->maxLength(255),
                                    ])
                                    ->columns(2),
                                Forms\Components\Section::make('Thông tin Client OCB')
                                    ->description('Cấu hình Client ID và Secret cho OCB API')
                                    ->schema([
                                        Forms\Components\TextInput::make('ocb_client_id')
                                            ->label('Client ID')
                                            ->required()
                                            ->maxLength(255),
                                        Forms\Components\TextInput::make('ocb_client_secret')
                                            ->label('Client Secret')
                                            ->password()
                                            ->required()
                                            ->maxLength(255),
                                    ])
                                    ->columns(2),
                                Forms\Components\Section::make('Token thông tin')
                                    ->description('Thông tin token hiện tại (chỉ đọc)')
                                    ->schema([
                                        Forms\Components\TextInput::make('ocb_access_token')
                                            ->label('Access Token')
                                            ->disabled()
                                            ->dehydrated(false),
                                        Forms\Components\TextInput::make('ocb_refresh_token')
                                            ->label('Refresh Token')
                                            ->disabled()
                                            ->dehydrated(false),
                                        Forms\Components\DateTimePicker::make('ocb_token_expires_at')
                                            ->label('Token hết hạn')
                                            ->disabled()
                                            ->dehydrated(false),
                                    ])
                                    ->columns(2),
                            ]),
                        Forms\Components\Tabs\Tab::make('MB Bank')
                            ->icon('heroicon-o-building-library')
                            ->schema([
                                Forms\Components\Section::make('Thông tin đăng nhập MB Bank')
                                    ->description('Cấu hình thông tin đăng nhập với ngân hàng MB Bank')
                                    ->schema([
                                        Forms\Components\TextInput::make('mb_username')
                                            ->label('Username')
                                            ->required()
                                            ->maxLength(255),
                                        Forms\Components\TextInput::make('mb_password')
                                            ->label('Password')
                                            ->password()
                                            ->required()
                                            ->maxLength(255),
                                    ])
                                    ->columns(2),
                                Forms\Components\Section::make('Token thông tin')
                                    ->description('Thông tin token hiện tại (chỉ đọc)')
                                    ->schema([
                                        Forms\Components\TextInput::make('mb_access_token')
                                            ->label('Access Token')
                                            ->disabled()
                                            ->dehydrated(false),
                                        Forms\Components\TextInput::make('mb_refresh_token')
                                            ->label('Refresh Token')
                                            ->disabled()
                                            ->dehydrated(false),
                                        Forms\Components\DateTimePicker::make('mb_token_expires_at')
                                            ->label('Token hết hạn')
                                            ->disabled()
                                            ->dehydrated(false),
                                    ])
                                    ->columns(2),
                            ]),
                        Forms\Components\Tabs\Tab::make('KLB Bank')
                            ->icon('heroicon-o-building-library')
                            ->schema([
                                Forms\Components\Section::make('Thông tin kết nối KLB Bank')
                                    ->description('Cấu hình thông tin kết nối với ngân hàng KLB Bank')
                                    ->schema([
                                        Forms\Components\TextInput::make('klb_client_id')
                                            ->label('Client ID')
                                            ->required()
                                            ->maxLength(255),
                                        Forms\Components\TextInput::make('klb_client_secret')
                                            ->label('Client Secret')
                                            ->password()
                                            ->required()
                                            ->maxLength(255),
                                        Forms\Components\TextInput::make('klb_client_encrypt_key')
                                            ->label('Client Encrypt Key')
                                            ->password()
                                            ->required()
                                            ->maxLength(255)
                                            ->helperText('Key mã hóa dữ liệu cho KLB Bank'),
                                    ])
                                    ->columns(2),
                                Forms\Components\Section::make('Token thông tin')
                                    ->description('Thông tin token hiện tại (chỉ đọc)')
                                    ->schema([
                                        Forms\Components\TextInput::make('klb_access_token')
                                            ->label('Access Token')
                                            ->disabled()
                                            ->dehydrated(false),
                                        Forms\Components\TextInput::make('klb_refresh_token')
                                            ->label('Refresh Token')
                                            ->disabled()
                                            ->dehydrated(false),
                                        Forms\Components\DateTimePicker::make('klb_token_expires_at')
                                            ->label('Token hết hạn')
                                            ->disabled()
                                            ->dehydrated(false),
                                    ])
                                    ->columns(2),
                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }
}
