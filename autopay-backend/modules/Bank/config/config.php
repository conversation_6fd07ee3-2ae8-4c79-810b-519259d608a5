<?php

return [
    'name' => 'Bank',

    /*
    |--------------------------------------------------------------------------
    | Bank Credentials Configuration
    |--------------------------------------------------------------------------
    |
    | Bank credentials loaded from environment variables for security.
    | These should never be stored in database or version control.
    |
    */
    'credentials' => [
        'ocb' => [
            'username' => env('OCB_USERNAME'),
            'password' => env('OCB_PASSWORD'),
            'client_id' => env('OCB_CLIENT_ID'),
            'client_secret' => env('OCB_CLIENT_SECRET'),
            'api_url' => env('OCB_API_URL', 'https://api.ocb.com.vn'),
        ],
        'mb' => [
            'username' => env('MB_USERNAME'),
            'password' => env('MB_PASSWORD'),
            'api_url' => env('MB_API_URL', 'https://api.mbbank.com.vn'),
        ],
        'klb' => [
            'client_id' => env('KLB_CLIENT_ID'),
            'client_secret' => env('KLB_CLIENT_SECRET'),
            'client_encrypt_key' => env('KLB_CLIENT_ENCRYPT_KEY'),
            'api_url' => env('KLB_API_URL', 'https://api.klb.com.vn'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Supported Banks Configuration
    |--------------------------------------------------------------------------
    |
    | This array contains configuration for all supported banks.
    | Each bank must have a strategy class that implements BankStrategyInterface.
    |
    */
    'supported_banks' => [
        'ocb' => [
            'name' => 'OceanBank',
            'display_name' => 'Ngân hàng TMCP Phương Đông (OCB)',
            'strategy_class' => \Modules\Bank\Strategies\OcbStrategy::class,
            'is_active' => true,
            'connection_fields' => ['accountNumber', 'idCardNumber', 'phoneNumber'],
            'features' => [
                'virtual_account',
                'account_validation',
                'transaction_history',
                'transaction_sync',
                'virtual_account_transactions',
                'account_linking',
            ],
            'supports' => [
                'virtual_account' => true,
                'balance_check' => false,
                'transaction_history' => true,
                'account_validation' => true,
                'transaction_sync' => true,
                'virtual_account_transactions' => true,
                'account_linking' => true,
            ],
            'virtual_account_prefix' => 'ITG',
            'api_config' => [
                'base_url' => env('OCB_API_BASE_URL', 'https://api.ocb.com.vn/corporates/partner'),
                'client_id' => env('OCB_CLIENT_ID'),
                'client_secret' => env('OCB_CLIENT_SECRET'),
                'username' => env('OCB_USERNAME'),
                'password' => env('OCB_PASSWORD'),
                'client_certificate' => env('OCB_CLIENT_CERTIFICATE'),
                'client_private_key' => env('OCB_CLIENT_PRIVATE_KEY'),
                'sandbox_certificate' => env('OCB_SANDBOX_CERTIFICATE'),
            ],
        ],

        'mb' => [
            'name' => 'MBBank',
            'display_name' => 'Ngân hàng TMCP Quân đội (MBBank)',
            'strategy_class' => \Modules\Bank\Strategies\MbStrategy::class,
            'is_active' => true, // Now implemented
            'connection_fields' => ['accountNumber', 'accountName', 'idCardNumber', 'phoneNumber'],
            'features' => [
                'virtual_account',
                'account_validation',
                'transaction_history',
                'balance_check',
                'transaction_sync',
                'virtual_account_transactions',
            ],
            'supports' => [
                'virtual_account' => true,
                'balance_check' => true,
                'transaction_history' => true,
                'account_validation' => true,
                'transaction_sync' => true,
                'virtual_account_transactions' => true,
            ],
            'virtual_account_prefix' => 'MB',
            'api_config' => [
                'base_url' => env('MB_API_BASE_URL'),
                'auth_token' => env('MB_AUTH_TOKEN'),
                'mb_public_key' => env('MB_PUBLIC_KEY'),
                'autopay_public_key' => env('AUTOPAY_PUBLIC_KEY'),
                'autopay_private_key' => env('AUTOPAY_PRIVATE_KEY'),
            ],
        ],

        'klb' => [
            'name' => 'KienLongBank',
            'display_name' => 'Ngân hàng TMCP Kiên Long',
            'strategy_class' => \Modules\Bank\Strategies\KlbStrategy::class,
            'is_active' => true, // Now implemented
            'connection_fields' => ['accountNumber'],
            'features' => [
                'virtual_account',
                'account_validation',
                'account_linking',
                'virtual_account_transactions',
            ],
            'supports' => [
                'virtual_account' => true,
                'balance_check' => false,
                'transaction_history' => false,
                'account_validation' => true,
                'account_linking' => true,
                'virtual_account_transactions' => true,
            ],
            'virtual_account_prefix' => '',
            'api_config' => [
                'base_url' => env('KLB_API_BASE_URL', 'https://api.kienlongbank.co/pay'),
                'client_id' => env('KLB_CLIENT_ID'),
                'secret_key' => env('KLB_SECRET_KEY'),
                'encrypt_key' => env('KLB_ENCRYPT_KEY'),
                'bank_account_no' => env('KLB_BANK_ACCOUNT_NO'),
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Bank Settings
    |--------------------------------------------------------------------------
    |
    | Default settings that apply to all banks unless overridden.
    |
    */
    'defaults' => [
        'timeout' => 30, // API timeout in seconds
        'retry_attempts' => 3,
        'retry_delay' => 1000, // milliseconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Feature Flags
    |--------------------------------------------------------------------------
    |
    | Global feature flags for bank module functionality.
    |
    */
    'features' => [
        'auto_sync_transactions' => env('BANK_AUTO_SYNC_TRANSACTIONS', true),
        'cache_account_validation' => env('BANK_CACHE_ACCOUNT_VALIDATION', true),
        'log_api_requests' => env('BANK_LOG_API_REQUESTS', true),
    ],
];
