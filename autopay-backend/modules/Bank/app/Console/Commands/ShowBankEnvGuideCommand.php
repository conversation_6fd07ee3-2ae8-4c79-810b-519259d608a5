<?php

namespace Modules\Bank\Console\Commands;

use Illuminate\Console\Command;

class ShowBankEnvGuideCommand extends Command
{
    protected $signature = 'bank:env-guide';
    
    protected $description = 'Show bank environment variables configuration guide';

    public function handle(): int
    {
        $this->info('🏦 Bank Environment Variables Configuration Guide');
        $this->line('');
        
        $this->info('📝 Add these variables to your .env file:');
        $this->line('');
        
        $this->comment('# OCB Bank Configuration');
        $this->line('OCB_USERNAME=your_ocb_username');
        $this->line('OCB_PASSWORD=your_ocb_password');
        $this->line('OCB_CLIENT_ID=your_ocb_client_id');
        $this->line('OCB_CLIENT_SECRET=your_ocb_client_secret');
        $this->line('OCB_API_URL=https://api.ocb.com.vn');
        $this->line('');
        
        $this->comment('# MB Bank Configuration');
        $this->line('MB_USERNAME=your_mb_username');
        $this->line('MB_PASSWORD=your_mb_password');
        $this->line('MB_API_URL=https://api.mbbank.com.vn');
        $this->line('');
        
        $this->comment('# KLB Bank Configuration');
        $this->line('KLB_CLIENT_ID=your_klb_client_id');
        $this->line('KLB_CLIENT_SECRET=your_klb_client_secret');
        $this->line('KLB_CLIENT_ENCRYPT_KEY=your_klb_encrypt_key');
        $this->line('KLB_API_URL=https://api.klb.com.vn');
        $this->line('');
        
        $this->info('🔧 After updating .env file:');
        $this->line('1. Clear config cache: php artisan config:clear');
        $this->line('2. Check configuration: php artisan bank:init-settings');
        $this->line('3. Test connections: php artisan bank:test-connection');
        $this->line('');
        
        $this->info('🔒 Security Notes:');
        $this->line('• Never commit .env file to version control');
        $this->line('• Use .env.example for documentation');
        $this->line('• Rotate credentials regularly');
        $this->line('• Keep backup of credentials in secure location');
        $this->line('');
        
        $this->info('📍 Current Status:');
        $banks = ['ocb', 'mb', 'klb'];
        foreach ($banks as $bank) {
            $credentials = config("bank.credentials.{$bank}");
            $configured = !empty(array_filter($credentials));
            $status = $configured ? '✅ Configured' : '❌ Not configured';
            $this->line("   " . strtoupper($bank) . " Bank: {$status}");
        }
        
        return Command::SUCCESS;
    }
}
