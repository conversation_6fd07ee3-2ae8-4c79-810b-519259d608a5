<?php

namespace Modules\Bank\Console\Commands;

use Illuminate\Console\Command;
use App\Settings\BankSettings;
use Carbon\Carbon;

class TestTokenTimestampCommand extends Command
{
    protected $signature = 'bank:test-token-timestamp {bank=ocb}';
    
    protected $description = 'Test token timestamp functionality';

    public function handle(): int
    {
        $bankCode = $this->argument('bank');
        $settings = app(BankSettings::class);
        
        $this->info("Testing token timestamp for {$bankCode} bank...");
        
        // Set a test token
        $testToken = 'test_token_' . time();
        $expiresAt = Carbon::now()->addHours(1)->toDateTimeString();
        
        $this->line("Setting test token: {$testToken}");
        $this->line("Expires at: {$expiresAt}");
        
        $settings->setAccessToken($bankCode, $testToken, $expiresAt);
        
        // Get token info
        $config = $settings->getAllBankConfigs()[$bankCode];
        
        $this->line('');
        $this->info('Token information:');
        $this->line("Access Token: " . ($config['access_token'] ?: 'Not set'));
        $this->line("Expires At: " . ($config['token_expires_at'] ?: 'Not set'));
        $this->line("Requested At: " . ($config['token_requested_at'] ?: 'Not set'));
        $this->line("Is Expired: " . ($config['is_token_expired'] ? 'Yes' : 'No'));
        
        // Test refresh token
        if ($this->confirm('Do you want to set a refresh token too?', true)) {
            $refreshToken = 'refresh_token_' . time();
            $this->line("Setting refresh token: {$refreshToken}");
            $settings->setRefreshToken($bankCode, $refreshToken);
            
            $config = $settings->getAllBankConfigs()[$bankCode];
            $this->line("Refresh Token: " . ($config['refresh_token'] ?: 'Not set'));
        }
        
        // Test clear tokens
        if ($this->confirm('Do you want to clear all tokens?', false)) {
            $settings->clearTokens($bankCode);
            $this->info('All tokens cleared!');
            
            $config = $settings->getAllBankConfigs()[$bankCode];
            $this->line("Access Token: " . ($config['access_token'] ?: 'Cleared'));
            $this->line("Expires At: " . ($config['token_expires_at'] ?: 'Cleared'));
            $this->line("Requested At: " . ($config['token_requested_at'] ?: 'Cleared'));
            $this->line("Refresh Token: " . ($config['refresh_token'] ?: 'Cleared'));
        }
        
        return Command::SUCCESS;
    }
}
