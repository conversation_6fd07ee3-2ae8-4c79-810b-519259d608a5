<?php

namespace Modules\Bank\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateBankSettingsCommand extends Command
{
    protected $signature = 'bank:update-settings';

    protected $description = 'Update bank settings structure';

    public function handle(): int
    {
        $this->info('Updating bank settings structure...');

        // Remove old settings
        DB::table('settings')->where('group', 'bank')->delete();

        // Insert new token-only settings (credentials are now in env)
        $settings = [
            // OCB Bank Token Settings
            ['group' => 'bank', 'name' => 'ocb_access_token', 'payload' => 'null', 'locked' => 0],
            ['group' => 'bank', 'name' => 'ocb_token_expires_at', 'payload' => 'null', 'locked' => 0],
            ['group' => 'bank', 'name' => 'ocb_refresh_token', 'payload' => 'null', 'locked' => 0],
            ['group' => 'bank', 'name' => 'ocb_token_requested_at', 'payload' => 'null', 'locked' => 0],

            // MB Bank Token Settings
            ['group' => 'bank', 'name' => 'mb_access_token', 'payload' => 'null', 'locked' => 0],
            ['group' => 'bank', 'name' => 'mb_token_expires_at', 'payload' => 'null', 'locked' => 0],
            ['group' => 'bank', 'name' => 'mb_refresh_token', 'payload' => 'null', 'locked' => 0],
            ['group' => 'bank', 'name' => 'mb_token_requested_at', 'payload' => 'null', 'locked' => 0],

            // KLB Bank Token Settings
            ['group' => 'bank', 'name' => 'klb_access_token', 'payload' => 'null', 'locked' => 0],
            ['group' => 'bank', 'name' => 'klb_token_expires_at', 'payload' => 'null', 'locked' => 0],
            ['group' => 'bank', 'name' => 'klb_refresh_token', 'payload' => 'null', 'locked' => 0],
            ['group' => 'bank', 'name' => 'klb_token_requested_at', 'payload' => 'null', 'locked' => 0],
        ];

        DB::table('settings')->insert($settings);

        $this->info('✅ Bank settings updated successfully!');

        return Command::SUCCESS;
    }
}
