<?php

namespace Modules\Bank\Console\Commands;

use Illuminate\Console\Command;
use Modules\Bank\Services\BankAuthService;
use App\Settings\BankSettings;

class TestBankConnectionCommand extends Command
{
    protected $signature = 'bank:test-connection {bank? : Bank code to test (ocb, mb, klb)}';

    protected $description = 'Test bank API connection and authentication';

    public function handle(): int
    {
        $bankCode = $this->argument('bank');
        $authService = new BankAuthService();
        $settings = app(BankSettings::class);

        if ($bankCode) {
            return $this->testSingleBank($bankCode, $authService, $settings);
        }

        // Test all banks
        $banks = ['ocb', 'mb', 'klb'];
        $results = [];

        foreach ($banks as $bank) {
            $results[$bank] = $this->testBankConnection($bank, $authService, $settings);
        }

        $this->displayResults($results);

        return Command::SUCCESS;
    }

    protected function testSingleBank(string $bankCode, BankAuthService $authService, BankSettings $settings): int
    {
        $this->info("Testing connection for {$bankCode} bank...");

        $result = $this->testBankConnection($bankCode, $authService, $settings);

        if ($result['success']) {
            $this->info("✅ {$bankCode}: {$result['message']}");
            if (isset($result['token_expires_at'])) {
                $this->line("   Token expires at: {$result['token_expires_at']}");
            }
        } else {
            $this->error("❌ {$bankCode}: {$result['message']}");
            if (isset($result['error_code'])) {
                $this->line("   Error code: {$result['error_code']}");
            }
        }

        return $result['success'] ? Command::SUCCESS : Command::FAILURE;
    }

    protected function testBankConnection(string $bankCode, BankAuthService $authService, BankSettings $settings): array
    {
        // Check if credentials are configured based on bank type
        $missingCredentials = [];

        if ($bankCode === 'ocb') {
            if (!$settings->getUsername($bankCode)) $missingCredentials[] = 'username';
            if (!$settings->getPassword($bankCode)) $missingCredentials[] = 'password';
            $credentials = $settings->getClientCredentials($bankCode);
            if (!$credentials['client_id']) $missingCredentials[] = 'client_id';
            if (!$credentials['client_secret']) $missingCredentials[] = 'client_secret';
        } elseif ($bankCode === 'mb') {
            if (!$settings->getUsername($bankCode)) $missingCredentials[] = 'username';
            if (!$settings->getPassword($bankCode)) $missingCredentials[] = 'password';
        } elseif ($bankCode === 'klb') {
            $credentials = $settings->getClientCredentials($bankCode);
            if (!$credentials['client_id']) $missingCredentials[] = 'client_id';
            if (!$credentials['client_secret']) $missingCredentials[] = 'client_secret';
            if (!$settings->getEncryptKey($bankCode)) $missingCredentials[] = 'encrypt_key';
        }

        if (!empty($missingCredentials)) {
            return [
                'success' => false,
                'message' => 'Missing credentials: ' . implode(', ', $missingCredentials),
                'error_code' => 'MISSING_CONFIG'
            ];
        }

        // Test authentication
        try {
            $token = $authService->authenticate($bankCode);

            if (!$token) {
                return [
                    'success' => false,
                    'message' => 'Authentication not yet implemented for ' . strtoupper($bankCode),
                    'error_code' => 'AUTH_NOT_IMPLEMENTED'
                ];
            }

            // Test connection
            return $authService->testConnection($bankCode);
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage(),
                'error_code' => 'CONNECTION_ERROR'
            ];
        }
    }

    protected function displayResults(array $results): void
    {
        $this->info('Bank Connection Test Results:');
        $this->line('');

        $headers = ['Bank', 'Status', 'Message', 'Token Expires'];
        $rows = [];

        foreach ($results as $bank => $result) {
            $status = $result['success'] ? '✅ Success' : '❌ Failed';
            $message = $result['message'];
            $expires = $result['token_expires_at'] ?? 'N/A';

            $rows[] = [
                strtoupper($bank),
                $status,
                $message,
                $expires
            ];
        }

        $this->table($headers, $rows);

        $successCount = count(array_filter($results, fn($r) => $r['success']));
        $totalCount = count($results);

        $this->line('');
        $this->info("Summary: {$successCount}/{$totalCount} banks connected successfully");
    }
}
