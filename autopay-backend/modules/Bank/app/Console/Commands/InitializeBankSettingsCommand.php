<?php

namespace Modules\Bank\Console\Commands;

use Illuminate\Console\Command;
use App\Settings\BankSettings;

class InitializeBankSettingsCommand extends Command
{
    protected $signature = 'bank:init-settings';

    protected $description = 'Initialize default bank settings';

    public function handle(): int
    {
        $this->info('Initializing bank settings...');

        $settings = app(BankSettings::class);
        $settings->initializeDefaults();

        $this->info('✅ Bank settings initialized successfully!');

        // Display current settings
        $this->info('Current bank configurations:');
        $configs = $settings->getAllBankConfigs();

        foreach ($configs as $bank => $config) {
            $this->line('');
            $this->line("📱 " . strtoupper($bank) . " Bank:");

            $credentials = $config['credentials'];
            if ($bank === 'ocb') {
                $this->line("   Username: " . ($credentials['username'] ? 'Set in ENV' : 'Missing OCB_USERNAME'));
                $this->line("   Password: " . ($credentials['password'] ? 'Set in ENV' : 'Missing OCB_PASSWORD'));
                $this->line("   Client ID: " . ($credentials['client_id'] ? 'Set in ENV' : 'Missing OCB_CLIENT_ID'));
                $this->line("   Client Secret: " . ($credentials['client_secret'] ? 'Set in ENV' : 'Missing OCB_CLIENT_SECRET'));
            } elseif ($bank === 'mb') {
                $this->line("   Username: " . ($credentials['username'] ? 'Set in ENV' : 'Missing MB_USERNAME'));
                $this->line("   Password: " . ($credentials['password'] ? 'Set in ENV' : 'Missing MB_PASSWORD'));
            } elseif ($bank === 'klb') {
                $this->line("   Client ID: " . ($credentials['client_id'] ? 'Set in ENV' : 'Missing KLB_CLIENT_ID'));
                $this->line("   Client Secret: " . ($credentials['client_secret'] ? 'Set in ENV' : 'Missing KLB_CLIENT_SECRET'));
                $this->line("   Encrypt Key: " . ($credentials['client_encrypt_key'] ? 'Set in ENV' : 'Missing KLB_CLIENT_ENCRYPT_KEY'));
            }

            $this->line("   Access Token: " . ($config['access_token'] ? 'Available' : 'Not available'));

            if ($config['access_token']) {
                $status = $config['is_token_expired'] ? '❌ Expired' : '✅ Valid';
                $this->line("   Token Status: {$status}");
                if ($config['token_expires_at']) {
                    $this->line("   Expires At: {$config['token_expires_at']}");
                }
                if ($config['token_requested_at']) {
                    $this->line("   Requested At: {$config['token_requested_at']}");
                }
            }
        }

        $this->line('');
        $this->info('💡 Next steps:');
        $this->line('1. Set environment variables in .env file for bank credentials');
        $this->line('2. Test connections using: php artisan bank:test-connection');

        return Command::SUCCESS;
    }
}
