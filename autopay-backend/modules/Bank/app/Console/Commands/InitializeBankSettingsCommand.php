<?php

namespace Modules\Bank\Console\Commands;

use Illuminate\Console\Command;
use App\Settings\BankSettings;

class InitializeBankSettingsCommand extends Command
{
    protected $signature = 'bank:init-settings';

    protected $description = 'Initialize default bank settings';

    public function handle(): int
    {
        $this->info('Initializing bank settings...');

        $settings = app(BankSettings::class);
        $settings->initializeDefaults();

        $this->info('✅ Bank settings initialized successfully!');

        // Display current settings
        $this->info('Current bank configurations:');
        $configs = $settings->getAllBankConfigs();

        foreach ($configs as $bank => $config) {
            $this->line('');
            $this->line("📱 " . strtoupper($bank) . " Bank:");

            if ($bank === 'ocb') {
                $this->line("   Username: " . ($config['username'] ? 'Set' : 'Not set'));
                $this->line("   Password: " . ($config['password'] ? 'Set' : 'Not set'));
                $this->line("   Client ID: " . ($config['client_credentials']['client_id'] ? 'Set' : 'Not set'));
                $this->line("   Client Secret: " . ($config['client_credentials']['client_secret'] ? 'Set' : 'Not set'));
            } elseif ($bank === 'mb') {
                $this->line("   Username: " . ($config['username'] ? 'Set' : 'Not set'));
                $this->line("   Password: " . ($config['password'] ? 'Set' : 'Not set'));
            } elseif ($bank === 'klb') {
                $this->line("   Client ID: " . ($config['client_credentials']['client_id'] ? 'Set' : 'Not set'));
                $this->line("   Client Secret: " . ($config['client_credentials']['client_secret'] ? 'Set' : 'Not set'));
                $this->line("   Encrypt Key: " . ($config['encrypt_key'] ? 'Set' : 'Not set'));
            }

            $this->line("   Access Token: " . ($config['access_token'] ? 'Available' : 'Not available'));

            if ($config['access_token']) {
                $status = $config['is_token_expired'] ? '❌ Expired' : '✅ Valid';
                $this->line("   Token Status: {$status}");
                if ($config['token_expires_at']) {
                    $this->line("   Expires At: {$config['token_expires_at']}");
                }
            }
        }

        $this->line('');
        $this->info('💡 Next steps:');
        $this->line('1. Configure client credentials in Filament admin panel');
        $this->line('2. Test connections using: php artisan bank:test-connection');

        return Command::SUCCESS;
    }
}
