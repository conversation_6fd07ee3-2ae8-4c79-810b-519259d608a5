<?php

namespace Modules\Bank\Services;

use App\Settings\BankSettings;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * Bank Authentication Service
 *
 * Handles authentication and token management for bank APIs
 */
class BankAuthService
{
    protected BankSettings $settings;

    public function __construct()
    {
        $this->settings = app(BankSettings::class);
    }

    /**
     * Get valid access token for bank
     */
    public function getAccessToken(string $bankCode): ?string
    {
        $token = $this->settings->getAccessToken($bankCode);

        if (!$token || $this->settings->isTokenExpired($bankCode)) {
            $token = $this->refreshAccessToken($bankCode);
        }

        return $token;
    }

    /**
     * Refresh access token using refresh token
     */
    public function refreshAccessToken(string $bankCode): ?string
    {
        try {
            $refreshToken = $this->settings->getRefreshToken($bankCode);

            if (!$refreshToken) {
                Log::warning("Missing refresh token for bank: {$bankCode}");
                return $this->authenticate($bankCode);
            }

            // Bank-specific refresh logic
            if ($bankCode === 'ocb') {
                return $this->refreshOcbToken($refreshToken);
            } elseif ($bankCode === 'mb') {
                return $this->refreshMbToken($refreshToken);
            } elseif ($bankCode === 'klb') {
                return $this->refreshKlbToken($refreshToken);
            }

            return null;
        } catch (\Exception $e) {
            Log::error("Error refreshing token for bank: {$bankCode}", [
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Authenticate with bank using bank-specific credentials
     */
    public function authenticate(string $bankCode): ?string
    {
        try {
            // Bank-specific authentication logic
            if ($bankCode === 'ocb') {
                return $this->authenticateOcb();
            } elseif ($bankCode === 'mb') {
                return $this->authenticateMb();
            } elseif ($bankCode === 'klb') {
                return $this->authenticateKlb();
            }

            Log::warning("Unsupported bank code: {$bankCode}");
            return null;
        } catch (\Exception $e) {
            Log::error("Error authenticating with bank: {$bankCode}", [
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Make authenticated HTTP request to bank API
     */
    public function makeAuthenticatedRequest(string $bankCode, string $method, string $endpoint, array $data = []): array
    {
        $token = $this->getAccessToken($bankCode);

        if (!$token) {
            return [
                'success' => false,
                'message' => 'Unable to get valid access token',
                'error_code' => 'AUTH_FAILED'
            ];
        }

        // Get bank-specific API URL
        $apiUrl = $this->getBankApiUrl($bankCode);
        if (!$apiUrl) {
            return [
                'success' => false,
                'message' => 'Bank API URL not configured',
                'error_code' => 'CONFIG_ERROR'
            ];
        }

        $url = rtrim($apiUrl, '/') . '/' . ltrim($endpoint, '/');

        try {
            $response = Http::withToken($token)
                ->timeout(30)
                ->{strtolower($method)}($url, $data);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                    'status' => $response->status()
                ];
            }

            // If unauthorized, try to refresh token and retry once
            if ($response->status() === 401) {
                Log::info("Token expired, attempting to refresh for bank: {$bankCode}");

                $newToken = $this->refreshAccessToken($bankCode);

                if ($newToken) {
                    $retryResponse = Http::withToken($newToken)
                        ->timeout(30)
                        ->{strtolower($method)}($url, $data);

                    if ($retryResponse->successful()) {
                        return [
                            'success' => true,
                            'data' => $retryResponse->json(),
                            'status' => $retryResponse->status()
                        ];
                    }
                }
            }

            return [
                'success' => false,
                'message' => 'API request failed',
                'error_code' => 'API_ERROR',
                'status' => $response->status(),
                'response' => $response->body()
            ];
        } catch (\Exception $e) {
            Log::error("Error making authenticated request to bank: {$bankCode}", [
                'error' => $e->getMessage(),
                'endpoint' => $endpoint
            ]);

            return [
                'success' => false,
                'message' => 'Request failed due to network error',
                'error_code' => 'NETWORK_ERROR'
            ];
        }
    }

    /**
     * Get API URL for specific bank
     */
    protected function getBankApiUrl(string $bankCode): ?string
    {
        return config("bank.credentials.{$bankCode}.api_url");
    }

    /**
     * Test bank connection
     */
    public function testConnection(string $bankCode): array
    {
        try {
            $token = $this->getAccessToken($bankCode);

            if (!$token) {
                return [
                    'success' => false,
                    'message' => 'Unable to authenticate with bank',
                    'error_code' => 'AUTH_FAILED'
                ];
            }

            // Try to make a simple API call to test connection
            $result = $this->makeAuthenticatedRequest($bankCode, 'GET', '/health');

            if ($result['success']) {
                return [
                    'success' => true,
                    'message' => 'Connection successful',
                    'token_expires_at' => $this->settings->getTokenExpiration($bankCode)
                ];
            }

            return $result;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage(),
                'error_code' => 'CONNECTION_FAILED'
            ];
        }
    }

    /**
     * Clear all tokens for a bank
     */
    public function clearTokens(string $bankCode): void
    {
        $this->settings->clearTokens($bankCode);
        Log::info("Cleared all tokens for bank: {$bankCode}");
    }

    /**
     * Get token status for a bank
     */
    public function getTokenStatus(string $bankCode): array
    {
        $token = $this->settings->getAccessToken($bankCode);
        $expiresAt = $this->settings->getTokenExpiration($bankCode);
        $isExpired = $this->settings->isTokenExpired($bankCode);

        return [
            'has_token' => !empty($token),
            'expires_at' => $expiresAt,
            'is_expired' => $isExpired,
            'expires_in_minutes' => $expiresAt ? Carbon::parse($expiresAt)->diffInMinutes(now()) : null
        ];
    }

    /**
     * Authenticate with OCB Bank
     */
    protected function authenticateOcb(): ?string
    {
        $credentials = config('bank.credentials.ocb');

        if (!$credentials['username'] || !$credentials['password'] || !$credentials['client_id'] || !$credentials['client_secret']) {
            Log::warning("Missing OCB credentials in config");
            return null;
        }

        // OCB-specific authentication logic here
        // This is a placeholder - implement actual OCB authentication
        Log::info("OCB authentication not yet implemented", [
            'username' => $credentials['username'],
            'api_url' => $credentials['api_url']
        ]);
        return null;
    }

    /**
     * Authenticate with MB Bank
     */
    protected function authenticateMb(): ?string
    {
        $credentials = config('bank.credentials.mb');

        if (!$credentials['username'] || !$credentials['password']) {
            Log::warning("Missing MB Bank credentials in config");
            return null;
        }

        // MB Bank-specific authentication logic here
        // This is a placeholder - implement actual MB Bank authentication
        Log::info("MB Bank authentication not yet implemented", [
            'username' => $credentials['username'],
            'api_url' => $credentials['api_url']
        ]);
        return null;
    }

    /**
     * Authenticate with KLB Bank
     */
    protected function authenticateKlb(): ?string
    {
        $credentials = config('bank.credentials.klb');

        if (!$credentials['client_id'] || !$credentials['client_secret'] || !$credentials['client_encrypt_key']) {
            Log::warning("Missing KLB Bank credentials in config");
            return null;
        }

        // KLB Bank-specific authentication logic here
        // This is a placeholder - implement actual KLB Bank authentication
        Log::info("KLB Bank authentication not yet implemented", [
            'client_id' => $credentials['client_id'],
            'api_url' => $credentials['api_url']
        ]);
        return null;
    }

    /**
     * Refresh OCB token
     */
    protected function refreshOcbToken(string $refreshToken): ?string
    {
        // OCB-specific token refresh logic
        Log::info("OCB token refresh not yet implemented");
        return null;
    }

    /**
     * Refresh MB Bank token
     */
    protected function refreshMbToken(string $refreshToken): ?string
    {
        // MB Bank-specific token refresh logic
        Log::info("MB Bank token refresh not yet implemented");
        return null;
    }

    /**
     * Refresh KLB Bank token
     */
    protected function refreshKlbToken(string $refreshToken): ?string
    {
        // KLB Bank-specific token refresh logic
        Log::info("KLB Bank token refresh not yet implemented");
        return null;
    }
}
