<?php

namespace Modules\Bank\Strategies;

use Modules\Bank\Contracts\BankStrategyInterface;
use Modules\Bank\DTOs\BankAccountValidationResult;
use Modules\Bank\DTOs\BankRegistrationResult;
use Modules\Bank\DTOs\TransactionHistoryResult;
use Modules\Bank\Integrations\Ocb\OcbConnector;
use Modules\Bank\Integrations\Ocb\Requests\AccessTokenRequest;
use Modules\Bank\Integrations\Ocb\Requests\VerifyMerchantInfoRequest;
use Modules\Bank\Integrations\Ocb\Requests\CreateVirtualAccountRequest;
use Modules\Bank\Integrations\Ocb\Requests\GetTransactionHistoryRequest;
use Modules\Bank\Services\BankAuthService;
use App\Settings\OpenBankingSettings;
use Exception;
use JsonException;
use RuntimeException;
use Saloon\Exceptions\Request\FatalRequestException;
use Saloon\Exceptions\Request\RequestException;
use Illuminate\Support\Facades\Log;

/**
 * OCB Bank Strategy Implementation
 *
 * Implements bank operations for OceanBank (OCB) using their API
 */
class OcbStrategy implements BankStrategyInterface
{
    protected OcbConnector $connector;
    protected array $config;
    protected BankAuthService $authService;

    public function __construct(array $config = [])
    {
        $this->config = $config;
        $this->connector = new OcbConnector();
        $this->authService = new BankAuthService();
    }

    /**
     * Check if account number is valid and get account information
     */
    public function checkAccountNumber(string $accountNumber, array $additionalData = []): BankAccountValidationResult
    {
        try {
            // Use BankAuthService for authenticated requests
            $result = $this->authService->makeAuthenticatedRequest('ocb', 'POST', '/verify-merchant', [
                'idCardNumber' => $additionalData['idCardNumber'] ?? '',
                'phoneNumber' => $additionalData['phoneNumber'] ?? '',
                'benBankAccount' => $accountNumber,
            ]);

            if (!$result['success']) {
                Log::warning('OCB account validation failed', [
                    'account_number' => $accountNumber,
                    'error' => $result['message'] ?? 'Unknown error',
                    'error_code' => $result['error_code'] ?? 'UNKNOWN'
                ]);

                return BankAccountValidationResult::failed(
                    'Có lỗi xảy ra trong quá trình kết nối với ngân hàng OCB. Vui lòng thử lại sau.',
                    $result['error_code'] ?? 'API_ERROR'
                );
            }

            $responseData = $result['data'];

            // Fallback to old method if auth service fails
            if (!$responseData) {
                return $this->checkAccountNumberLegacy($accountNumber, $additionalData);
            }

            // Check for invalid response
            if (isset($responseData['error']) || (isset($responseData['message']) && str_contains(strtolower($responseData['message']), 'invalid'))) {
                return BankAccountValidationResult::failed(
                    'Thông tin tài khoản OCB không hợp lệ, vui lòng kiểm tra lại.',
                    'INVALID_ACCOUNT'
                );
            }

            // Extract account name from OCB response structure
            $accountName = null;
            if (isset($responseData['data']['merchantInfo']['benBankAccountName'])) {
                $accountName = $responseData['data']['merchantInfo']['benBankAccountName'];
            }

            Log::info('OCB account validation successful', [
                'account_number' => $accountNumber,
                'account_name' => $accountName,
                'response_data' => $responseData
            ]);

            return BankAccountValidationResult::success(
                'Thông tin tài khoản OCB hợp lệ.',
                $accountName,
                $accountNumber,
                $responseData
            );

        } catch (FatalRequestException|RequestException|JsonException $e) {
            Log::error('OCB account validation error', [
                'account_number' => $accountNumber,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return BankAccountValidationResult::failed(
                'Có lỗi xảy ra trong quá trình kiểm tra tài khoản OCB. Vui lòng thử lại sau.',
                'NETWORK_ERROR'
            );
        } catch (Exception $e) {
            Log::error('OCB account validation unexpected error', [
                'account_number' => $accountNumber,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return BankAccountValidationResult::failed(
                'Có lỗi không mong muốn xảy ra khi kiểm tra tài khoản OCB. Vui lòng thử lại sau.',
                'UNEXPECTED_ERROR'
            );
        }
    }

    /**
     * Register account for banking services
     */
    public function registerAccount(array $accountData): BankRegistrationResult
    {
        try {
            // Get access token for OCB API
            $request = new AccessTokenRequest();
            $response = $this->connector->send($request);

            if ($response->failed()) {
                Log::error('OCB registration failed', [
                    'account_data' => $accountData,
                    'response_status' => $response->status(),
                    'response_body' => $response->body()
                ]);

                return BankRegistrationResult::failed(
                    'Không thể kết nối với hệ thống OCB. Vui lòng thử lại sau.',
                    'API_ERROR'
                );
            }

            $responseData = $response->json();

            // Save settings for future use with expiration time
            $settings = app(OpenBankingSettings::class);
            $settings->ocb = [
                ...$responseData,
                'expired_at' => now()->addSeconds($responseData['expires_in'] - 60),
                'registered_at' => now()->toISOString(),
                'account_data' => $accountData,
            ];
            $settings->save();

            Log::info('OCB account registered successfully', [
                'account_data' => $accountData,
                'access_token_expires_in' => $responseData['expires_in'] ?? null,
                'token_type' => $responseData['token_type'] ?? null
            ]);

            return BankRegistrationResult::success(
                'Đăng ký tài khoản OCB thành công. Access token đã được lưu.',
                [
                    'access_token' => $responseData['access_token'] ?? null,
                    'token_type' => $responseData['token_type'] ?? null,
                    'expires_in' => $responseData['expires_in'] ?? null,
                    'scope' => $responseData['scope'] ?? null,
                    'registered_at' => now()->toISOString(),
                ],
                $responseData['access_token'] ?? null
            );

        } catch (FatalRequestException|RequestException|JsonException $e) {
            Log::error('OCB registration error', [
                'account_data' => $accountData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return BankRegistrationResult::failed(
                'Có lỗi xảy ra trong quá trình đăng ký tài khoản OCB. Vui lòng thử lại sau.',
                'NETWORK_ERROR'
            );
        } catch (Exception $e) {
            Log::error('OCB registration unexpected error', [
                'account_data' => $accountData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return BankRegistrationResult::failed(
                'Có lỗi không mong muốn xảy ra trong quá trình đăng ký OCB. Vui lòng thử lại sau.',
                'UNEXPECTED_ERROR'
            );
        }
    }

    /**
     * Get transaction history for an account
     */
    public function getTransactionHistory(string $accountNumber, array $filters = []): TransactionHistoryResult
    {
        try {
            $requestFilters = [
                'accountNumber' => $accountNumber,
                'startDate' => $filters['start_date'] ?? date('Y-m-d', strtotime('-30 days')),
                'endDate' => $filters['end_date'] ?? date('Y-m-d'),
                'limit' => $filters['limit'] ?? 50,
                'page' => $filters['page'] ?? 1,
            ];

            $request = new GetTransactionHistoryRequest($requestFilters);
            $response = $this->connector->send($request);

            if ($response->failed()) {
                Log::warning('OCB transaction history failed', [
                    'account_number' => $accountNumber,
                    'filters' => $filters,
                    'response_status' => $response->status(),
                    'response_body' => $response->body()
                ]);

                return TransactionHistoryResult::failed(
                    'Có lỗi xảy ra trong quá trình lấy lịch sử giao dịch OCB. Vui lòng thử lại sau.',
                    'API_ERROR'
                );
            }

            $responseData = $response->json();
            $transactions = $responseData['data']['transactions'] ?? [];
            $totalCount = $responseData['data']['totalCount'] ?? count($transactions);

            Log::info('OCB transaction history retrieved successfully', [
                'account_number' => $accountNumber,
                'filters' => $filters,
                'transaction_count' => count($transactions),
                'total_count' => $totalCount
            ]);

            return TransactionHistoryResult::success(
                'Lấy lịch sử giao dịch OCB thành công.',
                $transactions,
                $totalCount,
                [
                    'page' => $requestFilters['page'],
                    'limit' => $requestFilters['limit'],
                    'total_pages' => ceil($totalCount / $requestFilters['limit']),
                ]
            );

        } catch (FatalRequestException|RequestException|JsonException $e) {
            Log::error('OCB transaction history error', [
                'account_number' => $accountNumber,
                'filters' => $filters,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return TransactionHistoryResult::failed(
                'Có lỗi xảy ra trong quá trình lấy lịch sử giao dịch OCB. Vui lòng thử lại sau.',
                'NETWORK_ERROR'
            );
        } catch (Exception $e) {
            Log::error('OCB transaction history unexpected error', [
                'account_number' => $accountNumber,
                'filters' => $filters,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return TransactionHistoryResult::failed(
                'Có lỗi không mong muốn xảy ra khi lấy lịch sử giao dịch OCB. Vui lòng thử lại sau.',
                'UNEXPECTED_ERROR'
            );
        }
    }

    /**
     * Create virtual account (if supported by bank)
     */
    public function createVirtualAccount(array $virtualAccountData): array
    {
        try {
            $requestData = [
                'accountNumber' => $virtualAccountData['account_number'] ?? '',
                'accountName' => $virtualAccountData['account_name'] ?? '',
                'virtualAccountPrefix' => $virtualAccountData['prefix'] ?? $this->config['virtual_account_prefix'] ?? 'ITG',
                'description' => $virtualAccountData['description'] ?? 'Virtual Account for AutoPay',
            ];

            $request = new CreateVirtualAccountRequest($requestData);
            $response = $this->connector->send($request);

            if ($response->failed()) {
                Log::error('OCB virtual account creation failed', [
                    'virtual_account_data' => $virtualAccountData,
                    'response_status' => $response->status(),
                    'response_body' => $response->body()
                ]);

                return [
                    'success' => false,
                    'message' => 'Có lỗi xảy ra trong quá trình tạo tài khoản ảo OCB. Vui lòng thử lại sau.',
                    'error_code' => 'API_ERROR'
                ];
            }

            $responseData = $response->json();
            $virtualAccountInfo = $responseData['data']['virtualAccountData'] ?? [];

            Log::info('OCB virtual account created successfully', [
                'virtual_account_data' => $virtualAccountData,
                'response_data' => $responseData,
                'virtual_account_info' => $virtualAccountInfo
            ]);

            return [
                'success' => true,
                'message' => 'Tạo tài khoản ảo OCB thành công.',
                'virtual_account_number' => $virtualAccountInfo['virtualAccountNumber'] ?? null,
                'virtual_account_name' => $virtualAccountInfo['virtualAccountName'] ?? null,
                'data' => $responseData
            ];

        } catch (FatalRequestException|RequestException|JsonException $e) {
            Log::error('OCB virtual account creation error', [
                'virtual_account_data' => $virtualAccountData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình tạo tài khoản ảo OCB. Vui lòng thử lại sau.',
                'error_code' => 'NETWORK_ERROR'
            ];
        } catch (Exception $e) {
            Log::error('OCB virtual account creation unexpected error', [
                'virtual_account_data' => $virtualAccountData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi không mong muốn xảy ra khi tạo tài khoản ảo OCB. Vui lòng thử lại sau.',
                'error_code' => 'UNEXPECTED_ERROR'
            ];
        }
    }

    /**
     * Delete/disable virtual account (if supported by bank)
     */
    public function deleteVirtualAccount(array $virtualAccountData): array
    {
        Log::info('OCB virtual account deletion requested', [
            'virtual_account_data' => $virtualAccountData
        ]);

        return [
            'success' => false,
            'message' => 'Tính năng xóa tài khoản ảo OCB đang được phát triển.',
            'error_code' => 'NOT_IMPLEMENTED'
        ];
    }

    /**
     * Get virtual account transactions
     */
    public function getVirtualAccountTransactions(array $filters = []): array
    {
        Log::info('OCB virtual account transactions requested', [
            'filters' => $filters
        ]);

        return [
            'success' => false,
            'message' => 'Tính năng lấy giao dịch tài khoản ảo OCB đang được phát triển.',
            'error_code' => 'NOT_IMPLEMENTED',
            'transactions' => []
        ];
    }

    /**
     * Link account to banking services
     */
    public function linkAccount(string $accountNumber): array
    {
        Log::info('OCB account linking requested', [
            'account_number' => $accountNumber
        ]);

        return [
            'success' => false,
            'message' => 'Tính năng liên kết tài khoản OCB đang được phát triển.',
            'error_code' => 'NOT_IMPLEMENTED'
        ];
    }

    /**
     * Verify linked account with OTP
     */
    public function verifyLinkedAccount(array $verificationData): array
    {
        Log::info('OCB account verification requested', [
            'verification_data' => $verificationData
        ]);

        return [
            'success' => false,
            'message' => 'Tính năng xác thực tài khoản OCB đang được phát triển.',
            'error_code' => 'NOT_IMPLEMENTED'
        ];
    }

    /**
     * Get account balance (if supported by bank)
     */
    public function getAccountBalance(string $accountNumber): ?float
    {
        // OCB doesn't support balance check according to the config
        return null;
    }

    /**
     * Check if bank supports specific feature
     */
    public function supportsFeature(string $feature): bool
    {
        $supportedFeatures = $this->config['features'] ?? [];
        return in_array($feature, $supportedFeatures);
    }

    /**
     * Get bank configuration
     */
    public function getBankConfig(): array
    {
        return $this->config;
    }

    /**
     * Sync transaction with bank (optional method for banks that support it)
     */
    public function syncTransaction(array $transactionData): array
    {
        try {
            // OCB transaction sync is typically handled via webhook
            // This method processes incoming transaction data from OCB

            $bankTransactionId = $transactionData['bank_transaction_id'] ?? '';
            $amount = $transactionData['amount'] ?? 0;
            $description = $transactionData['description'] ?? '';
            $timestamp = $transactionData['timestamp'] ?? now()->toISOString();

            // Validate required fields
            if (empty($bankTransactionId)) {
                return [
                    'success' => false,
                    'message' => 'Bank transaction ID is required for OCB transaction sync.',
                    'error_code' => 'MISSING_TRANSACTION_ID'
                ];
            }

            // Process the transaction sync
            Log::info('OCB transaction sync processing', [
                'bank_transaction_id' => $bankTransactionId,
                'amount' => $amount,
                'description' => $description,
                'timestamp' => $timestamp
            ]);

            // Here you would typically:
            // 1. Validate the transaction signature (if provided)
            // 2. Update local transaction records
            // 3. Trigger any business logic (notifications, etc.)

            return [
                'success' => true,
                'message' => 'OCB transaction sync processed successfully.',
                'code' => '00',
                'ref_transaction_id' => $bankTransactionId,
                'data' => [
                    'bank_transaction_id' => $bankTransactionId,
                    'amount' => $amount,
                    'description' => $description,
                    'processed_at' => now()->toISOString(),
                ]
            ];

        } catch (Exception $e) {
            Log::error('OCB transaction sync error', [
                'transaction_data' => $transactionData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình đồng bộ giao dịch OCB.',
                'error_code' => 'INTERNAL_ERROR',
                'code' => '01',
                'ref_transaction_id' => $transactionData['bank_transaction_id'] ?? null,
            ];
        }
    }

    /**
     * Legacy method for account validation using direct connector
     */
    protected function checkAccountNumberLegacy(string $accountNumber, array $additionalData = []): BankAccountValidationResult
    {
        try {
            $request = new VerifyMerchantInfoRequest([
                'idCardNumber' => $additionalData['idCardNumber'] ?? '',
                'phoneNumber' => $additionalData['phoneNumber'] ?? '',
                'benBankAccount' => $accountNumber,
            ]);

            $response = $this->connector->send($request);

            if ($response->failed()) {
                Log::warning('OCB account validation failed (legacy)', [
                    'account_number' => $accountNumber,
                    'response_status' => $response->status(),
                    'response_body' => $response->body()
                ]);

                return BankAccountValidationResult::failed(
                    'Có lỗi xảy ra trong quá trình kết nối với ngân hàng OCB. Vui lòng thử lại sau.',
                    'API_ERROR'
                );
            }

            $responseBody = $response->body();
            if (str_contains(strtolower($responseBody), 'invalid')) {
                return BankAccountValidationResult::failed(
                    'Thông tin tài khoản OCB không hợp lệ, vui lòng kiểm tra lại.',
                    'INVALID_ACCOUNT'
                );
            }

            $responseData = $response->json();

            // Extract account name from OCB response structure
            $accountName = null;
            if (isset($responseData['data']['merchantInfo']['benBankAccountName'])) {
                $accountName = $responseData['data']['merchantInfo']['benBankAccountName'];
            }

            return BankAccountValidationResult::success(
                'Tài khoản OCB hợp lệ.',
                $accountName,
                $responseData
            );
        } catch (Exception $e) {
            Log::error('OCB account validation error (legacy)', [
                'account_number' => $accountNumber,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return BankAccountValidationResult::failed(
                'Có lỗi xảy ra trong quá trình kiểm tra tài khoản OCB.',
                'INTERNAL_ERROR'
            );
        }
    }
}
