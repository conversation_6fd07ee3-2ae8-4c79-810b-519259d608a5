<?php

namespace App\Http\Controllers;

use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Symfony\Component\HttpFoundation\Response;

class KlbController extends Controller
{
    //    protected static string $baseUrl = 'https://api-staging.kienlongbank.co/pay';

    protected static string $baseUrl = 'https://api.kienlongbank.co/pay';

    //    protected static string $clientId = '4c1f9424-195d-496d-b574-6245b14a9ad6';
    protected static string $clientId = '773a2b23-a539-450f-835c-db639adaf61a';

    //    protected static string $secretKey = 'Y0XQaNLR80shcf7KnoJyjelyHrq/kjR+ul8penYY2GRi7fHezvf3GkJ4YBQ58bujaRn+7w+Ads1OrAmmcgoZuatkIKaPH3U7/Fuz8Ww3AiPoHaOyyUF2lSyB6vxkvhWPRbtZXXHi+HObZLDuUK+xZO9e/+4KNdBA75Zhn4dM+YcIGUiSKXpAn1N+iBOPw9KO0ZvjKvyxDHzVVVGhiKhcFUNex8eQKKjGsUcwvLT8Q0L/KS0ZvNouAY+Kf83fi+iChAAAgTK0E6DWheGRH12TcNvExR1HNX3btFPa7KwHUziqt2ouaJ2aUWH51ObWqYGCdUVsndngapWVkMNNqYsKdQ==';
    protected static string $secretKey = 'sOS2KzRRNa51jVGpR0Oi04D4MyAZ6dTRvUbcxe07I0ror/JeUoMny1T+SSGPxpUnR4rnyeOwQxZSC+7XdAFhW6sTil1OIJSYQVqSdPJUAFA3hFWrmP+6rhPxvCCHqjt/dzRdBptMe3M/d/8z9Tvp7c6Dvm6x7u/allLWiF8ea/fw4jDXDe8dwq3yjNDSwRW5vjso3sZ2KI91hV7guU/ljX4r0jdxY8SI2lQ6Mldzyfyq3PzRuxTcClj4MQg8+/sbh2rbj35LLWJvKCMyJ1Z8sqEqjOLzY76JAFV0coo5Xb5WhncP/wvBk9/H3fWdAxz2ZG47nh59oVSZ9/LQI+oyFg==';

    //    protected static string $encryptKey = '8C8F817B52B36EAEAD4AAB61C7BDFA8BFEF6B64F302493C4A9F50ED1942249A1';
    protected static string $encryptKey = 'A36429000CB1ECCBFB43736D78696306EEE5E83E0DF350407326FD688A3B0719';

    //    protected static string $bankAccountNo = '**********';
    protected static string $bankAccountNo = '**********';

    public function tokenGenerate(Request $request): Response
    {
        ray('tokenGenerate', $request->header('authorization'), $request->all());

        $data = [
            'access_token' => 'a3e7ak92-333b-4078-8eeb-3a620051',
            'token_type' => 'bearer',
            'expires_in' => 59,
        ];

        return response()->json($data);
    }

    public function transactionSync(Request $request): Response
    {
        ray('transactionSync', $request->headers->all(), $request->all(), json_encode($request->all()));

        $requestClientId = $request->header('x-api-client');
        ray('requestClientId', $requestClientId);

        $timestamp = $request->header('x-api-time');
        ray('timestamp', $request->header('x-api-time'));

        $encryptedData = $request->json('data');

        ray('request x-api-validate', $request->header('x-api-validate'));

        $apiValidate = $this->hmacEncode(self::$clientId.'|'.$timestamp.'|'.$encryptedData, self::$secretKey);
        ray('system x-api-validate', $apiValidate);

        $data = json_decode($this->decryptAES($encryptedData, self::$encryptKey));
        ray($data);

        /*
        {
            +"transactionId": "7e67cbf3-2f52-4c63-8078-71b53598e387"
            +"virtualAccount": "118399100244000011"
            +"actualAccount": "**********"
            +"fromBin": "970452"
            +"fromAccount": "********"
            +"success": true
            +"amount": 100000
            +"txnNumber": "P00000084024"
            +"transferDesc": "[118399100244000011.**********] donate"
            +"time": "2024-12-10 13:46:24"
        }
         */

        return response()->json($request->all());
    }

    protected function encryptAES(string $data, string $key): string
    {
        $iv = hex2bin(substr($key, 0, 32));
        $pri_key = hex2bin($key);
        $data_utf8 = mb_convert_encoding($data, 'UTF-8');
        $encrypted_data = openssl_encrypt(
            $data_utf8,
            'aes-256-cbc',
            $pri_key,
            OPENSSL_RAW_DATA,
            $iv
        );

        return base64_encode($encrypted_data);
    }

    protected function decryptAES(string $data, string $key): string
    {
        $iv = hex2bin(substr($key, 0, 32));
        $key = hex2bin($key);

        return openssl_decrypt(
            base64_decode($data),
            'aes-256-cbc',
            $key,
            OPENSSL_RAW_DATA,
            $iv
        );
    }

    protected function hmacEncode(string $data, string $key): string
    {
        $hmac = hash_hmac('sha256', $data, $key, true);

        return bin2hex($hmac);
    }

    /**
     * @throws ConnectionException
     */
    public function createVa(): void
    {
        $endpoint = self::$baseUrl.'/api/payment/v1/virtualAccount/enable';
        $timestamp = time() * 1000;
        ray('timestamp', $timestamp);

        $payload = json_encode([
            'order' => 1,
            'timeout' => 0,
            'fixAmount' => 0,
            'fixContent' => 'donate',
            'bankAccountNo' => self::$bankAccountNo,
        ]);

        ray('payload', $payload);

        $encryptedData = $this->encryptAES($payload, self::$encryptKey);
        ray('encryptedData', $encryptedData);
        $apiValidate = $this->hmacEncode(self::$clientId.'|'.$timestamp.'|'.$encryptedData, self::$secretKey);
        ray('apiValidate', $apiValidate);

        $data = json_encode([
            'data' => $encryptedData,
        ]);
        ray('data', $data);

        ray('headers', [
            'x-api-client' => self::$clientId,
            'x-api-time' => $timestamp,
            'x-api-validate' => $apiValidate,
            'Content-Type' => 'application/json',
        ]);

        $response = Http::asJson()->withHeaders([
            'x-api-client' => self::$clientId,
            'x-api-time' => $timestamp,
            'x-api-validate' => $apiValidate,
            //            'Content-Type' => 'application/json',
        ])->post($endpoint, [
            'data' => $encryptedData,
        ]);

        ray(data_get($response->json(), 'data', false) ? json_decode($this->decryptAES($response->json('data'), self::$encryptKey)) : $response->json());
    }

    /**
     * @throws ConnectionException
     */
    public function deleteVa(): void
    {
        $endpoint = self::$baseUrl.'/api/payment/v1/virtualAccount/disable';
        $timestamp = time() * 1000;
        ray('timestamp', $timestamp);

        $payload = json_encode([
            'order' => 1,
        ]);
        ray('payload', $payload);

        $encryptedData = $this->encryptAES($payload, self::$encryptKey);
        ray('encryptedData', $encryptedData);
        $apiValidate = $this->hmacEncode(self::$clientId.'|'.$timestamp.'|'.$encryptedData, self::$secretKey);
        ray('apiValidate', $apiValidate);

        $data = json_encode([
            'data' => $encryptedData,
        ]);
        ray('data', $data);

        ray('headers', [
            'x-api-client' => self::$clientId,
            'x-api-time' => $timestamp,
            'x-api-validate' => $apiValidate,
        ]);

        $response = Http::asJson()->withHeaders([
            'x-api-client' => self::$clientId,
            'x-api-time' => $timestamp,
            'x-api-validate' => $apiValidate,
            //            'Content-Type' => 'application/json',
        ])->post($endpoint, [
            'data' => $encryptedData,
        ]);

        ray(data_get($response->json(), 'data', false) ? json_decode($this->decryptAES($response->json('data'), self::$encryptKey)) : $response->json());
    }

    /**
     * @throws ConnectionException
     */
    public function getVaTransaction(): void
    {
        $endpoint = self::$baseUrl.'/api/payment/v1/getTransaction';
        $timestamp = time() * 1000;
        ray('timestamp', $timestamp);

        $payload = json_encode([
            'order' => 1,
            'page' => 0,
            'size' => 500, // max: 500
            'bankAccountNo' => self::$bankAccountNo,
            'fromDate' => '2024-12-10 00:00:00',
            'toDate' => '2024-12-10 23:59:59',

        ]);
        ray('payload', $payload);

        $encryptedData = $this->encryptAES($payload, self::$encryptKey);
        ray('encryptedData', $encryptedData);
        $apiValidate = $this->hmacEncode(self::$clientId.'|'.$timestamp.'|'.$encryptedData, self::$secretKey);
        ray('apiValidate', $apiValidate);

        $data = json_encode([
            'data' => $encryptedData,
        ]);
        ray('data', $data);

        $response = Http::asJson()->withHeaders([
            'x-api-client' => self::$clientId,
            'x-api-time' => $timestamp,
            'x-api-validate' => $apiValidate,
        ])->post($endpoint, [
            'data' => $encryptedData,
        ]);

        ray(data_get($response->json(), 'data', false) ? json_decode($this->decryptAES($response->json('data'), self::$encryptKey)) : $response->json());
    }

    /**
     * @throws ConnectionException
     */
    public function checkAccountNo(): void
    {
        $endpoint = self::$baseUrl.'/api/openBanking/v1/checkAccountNo';
        $timestamp = time() * 1000;
        ray('timestamp', $timestamp);

        $payload = json_encode([
            'accountNo' => self::$bankAccountNo,
        ]);
        ray('payload', $payload);

        $encryptedData = $this->encryptAES($payload, self::$encryptKey);
        ray('encryptedData', $encryptedData);
        $apiValidate = $this->hmacEncode(self::$clientId.'|'.$timestamp.'|'.$encryptedData, self::$secretKey);
        ray('apiValidate', $apiValidate);

        $data = json_encode([
            'data' => $encryptedData,
        ]);
        ray('data', $data);

        $response = Http::asJson()->withHeaders([
            'x-api-client' => self::$clientId,
            'x-api-time' => $timestamp,
            'x-api-validate' => $apiValidate,
        ])->post($endpoint, [
            'data' => $encryptedData,
        ]);

        ray(data_get($response->json(), 'data', false) ? json_decode($this->decryptAES($response->json('data'), self::$encryptKey)) : $response->json());
    }

    /**
     * @throws ConnectionException
     */
    public function linkAccountNo(): void
    {
        $endpoint = self::$baseUrl.'/api/openBanking/v1/linkAccount';
        $timestamp = time() * 1000;
        ray('timestamp', $timestamp);

        $payload = json_encode([
            'accountNo' => self::$bankAccountNo,
        ]);
        ray('payload', $payload);

        $encryptedData = $this->encryptAES($payload, self::$encryptKey);
        ray('encryptedData', $encryptedData);
        $apiValidate = $this->hmacEncode(self::$clientId.'|'.$timestamp.'|'.$encryptedData, self::$secretKey);
        ray('apiValidate', $apiValidate);

        $data = json_encode([
            'data' => $encryptedData,
        ]);
        ray('data', $data);

        $response = Http::asJson()->withHeaders([
            'x-api-client' => self::$clientId,
            'x-api-time' => $timestamp,
            'x-api-validate' => $apiValidate,
        ])->post($endpoint, [
            'data' => $encryptedData,
        ]);

        ray(data_get($response->json(), 'data', false) ? json_decode($this->decryptAES($response->json('data'), self::$encryptKey)) : $response->json());
    }

    /**
     * @throws ConnectionException
     */
    public function verifyLinkAccountNo(): void
    {
        $endpoint = self::$baseUrl.'/api/openBanking/v1/linkAccount/verify';
        $timestamp = time() * 1000;
        ray('timestamp', $timestamp);

        $payload = json_encode([
            'sessionId' => 'f0d3abf0-eb7d-4637-9a27-8f189ec3cfa0',
            'otp' => '895983',
            'accountNo' => self::$bankAccountNo,
        ]);
        ray('payload', $payload);

        $encryptedData = $this->encryptAES($payload, self::$encryptKey);
        ray('encryptedData', $encryptedData);
        $apiValidate = $this->hmacEncode(self::$clientId.'|'.$timestamp.'|'.$encryptedData, self::$secretKey);
        ray('apiValidate', $apiValidate);

        $data = json_encode([
            'data' => $encryptedData,
        ]);
        ray('data', $data);

        $response = Http::asJson()->withHeaders([
            'x-api-client' => self::$clientId,
            'x-api-time' => $timestamp,
            'x-api-validate' => $apiValidate,
        ])->post($endpoint, [
            'data' => $encryptedData,
        ]);

        ray(data_get($response->json(), 'data', false) ? json_decode($this->decryptAES($response->json('data'), self::$encryptKey)) : $response->json());
    }
}
