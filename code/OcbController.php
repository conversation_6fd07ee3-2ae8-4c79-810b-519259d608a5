<?php

namespace App\Http\Controllers;

use App\Helpers\SignatureHelper;
use Illuminate\Http\Request;
use JsonException;
use Symfony\Component\HttpFoundation\Response;

class OcbController extends Controller
{
    /*
    protected static string $sandboxCertificate = '-----BEGIN CERTIFICATE-----
MIID0zCCArugAwIBAgIULpMPKsuU7TojkfH2zMzSogDd1aYwDQYJKoZIhvcNAQEL
BQAweDELMAkGA1UEBhMCVk4xDDAKBgNVBAgMA0hDTTEMMAoGA1UEBwwDSENNMQ0w
CwYDVQQKDARVRFROMQ0wCwYDVQQLDARVRFROMQwwCgYDVQQDDANPQ0IxITAfBgkq
hkiG9w0BCQEWEmhpZXVubTFAb2NiLmNvbS52bjAgFw0yMzExMTcwMjM5NTlaGA8y
MTIzMTAyNDAyMzk1OVoweDELMAkGA1UEBhMCVk4xDDAKBgNVBAgMA0hDTTEMMAoG
A1UEBwwDSENNMQ0wCwYDVQQKDARVRFROMQ0wCwYDVQQLDARVRFROMQwwCgYDVQQD
DANPQ0IxITAfBgkqhkiG9w0BCQEWEmhpZXVubTFAb2NiLmNvbS52bjCCASIwDQYJ
KoZIhvcNAQEBBQADggEPADCCAQoCggEBAJ3t5TKrhn4opbV4mJRYhapqIOAqpFB+
oZuZy9cc8wMkQkzdq8OybwARmwqNhDUiQQsp9rqJxZDkJ3NcW0KsulakE2e+RHCx
TswtzetBuoYfCA3fkSqIomIiXAZjBEXrcpXOy0ropUTB8jLRCEzhhFzXaFHppjZH
5I1Clq0bsL3LkTrX2udLjbsqjQnQ/XnLxRxpPn57uiXg4DUgd4XHWDW9PiEl7Ctd
X6muhZjCCvLX+GVFsiAo3xXZSjohICKMLjZwQ6wK/OJBVsi9mHzgNRpaIgrC3t7O
6MlzI9RCN2AapBeVUVg6rNx1a7zhVzCh5naausFnI2YEm7MO5HYIsjUCAwEAAaNT
MFEwHQYDVR0OBBYEFMw4740IFudtSSN0YF9dplPFWVuJMB8GA1UdIwQYMBaAFMw4
740IFudtSSN0YF9dplPFWVuJMA8GA1UdEwEB/wQFMAMBAf8wDQYJKoZIhvcNAQEL
BQADggEBAEliCf/Z1bSE09alLYjs248/sT0AvFZLu7iZCeWLLDrEDLdZVMW0OVmU
v6SyFDBkX1ZAt5DWXVjsGmMTICY0Bh4luSTEJDBD70YLcAydA9Z/77bDYpy50r4z
+EHZ5LqtNZVvP1rqlAcGo5eKgLDN7xjMx3FD5AuWYqzCw9xW7cADF6IBFc9GH/KV
t1hMfmVNupxgriL/lS9/EVQe63kwJVhzpEuFVvQkJTR57ORLCSsZo6hGo5ksY6qQ
8RrFxbeIOHV60JrBnNdWG+hnfTvh3HnynCKQS3Xrg1kVt39ahXcxMJi0148wUhEk
18jm6IhsLbTtkq5cjRPwtSr/dayqIZQ=
-----END CERTIFICATE-----';
    */

    protected static string $sandboxCertificate = '-----BEGIN CERTIFICATE-----
MIIEIzCCAwugAwIBAgIUVLYEUxdSesogLkmeZDROfpjjQ8IwDQYJKoZIhvcNAQEL
BQAwgZ8xCzAJBgNVBAYTAlZOMQwwCgYDVQQIDANIQ00xDDAKBgNVBAcMA0hDTTEn
MCUGA1UECgweTmdhbiBoYW5nIFRNQ1AgUGh1b25nIERvbmcgT0NCMTYwNAYDVQQL
DC1LaG9pIENvbmcgTmdoZSAtIFRydW5nIFRhbSBVbmcgRHVuZyBDb25nIE5naGUx
EzARBgNVBAMMCm9jYi5jb20udm4wIBcNMjUwMTE2MDcyMzU2WhgPMjEyNDEyMjMw
NzIzNTZaMIGfMQswCQYDVQQGEwJWTjEMMAoGA1UECAwDSENNMQwwCgYDVQQHDANI
Q00xJzAlBgNVBAoMHk5nYW4gaGFuZyBUTUNQIFBodW9uZyBEb25nIE9DQjE2MDQG
A1UECwwtS2hvaSBDb25nIE5naGUgLSBUcnVuZyBUYW0gVW5nIER1bmcgQ29uZyBO
Z2hlMRMwEQYDVQQDDApvY2IuY29tLnZuMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8A
MIIBCgKCAQEAqnj7b9oEcKVuurSaraicNGkPiGursd1S/mMzrwVlbTdLo/FSqoIa
gdV+ZMdw4c2dTlBE32hX09nbk4pEkF0CWdOZmivneMxkO7wBrnvVuEIieTLsuLLZ
rvyX4a3qknJc3S8r9/pZBCUrBaZabQn5CWYApRKJKJ73e2AmKKKOEJ81tTt0tcsS
36uZFCKiqyPvFSzv6fq+m4p6qRNms14+2C45Il4y9bUp93hOZ9XEg46ik3YuLh90
T5Ao6EFk5a8IfyJhLX7K+e4veJWmIFw2/mL0b7Yl4+QKzWcAK49eUSsx7hxnwxWb
Uha134qwf/J7YL6ZcwVmLguUaX8tEVqnIwIDAQABo1MwUTAdBgNVHQ4EFgQUIf4G
FYgHIqZYz5qN2p4pCcSA9CQwHwYDVR0jBBgwFoAUIf4GFYgHIqZYz5qN2p4pCcSA
9CQwDwYDVR0TAQH/BAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEATqH38iG60UuM
VYuCBK46q4s4d2kn004AJviWRgXzM0VfUNEmpFblSZw0rYxZNBLhE7UVurTU6vsr
3pIgNBZ74VImCEpvPL+DP5LHwLMSko8HYDZEaerTAPpJy3UhCtl+lUdfJtrtCHvb
TjWHbM034Wlqvq027/wsBHpcSohw/PRdpDAexsKFqklC8PrSL/MbtEgY58yARBiM
heEEqdo3yFELqlH1N3fIZ840Aco1mlaS2UROWE2vmkft9vjCoJW4VqqgSRVfm7CF
awrEBtJLsCXCPBJTx4vbm+yupwPvkrq8WdFRNM3fnhw40YbL5vSDrBpML6yawkUx
t6FzzwU+Sg==
-----END CERTIFICATE-----';

    /**
     * @throws JsonException
     */
    public function transactionSync(Request $request): Response
    {

        // Receive Heartbeat Elastic from OCB with empty payload
        if (empty($request->all())) {
            return response()->json([
                'code' => '00',
                'message' => 'Pong',
            ]);
        }

        ray(json_encode($request->all()), $request->all());
        ray(json_encode($request->headers->all()), $request->headers->all());
        $timestamp = $request->header('x-request-timestamp');
        $signature = $request->header('x-signature');
        $payload = json_encode($request->all(), JSON_THROW_ON_ERROR);

        $isVerified = SignatureHelper::verifyX509Certificate($timestamp.':'.$payload, $signature, self::$sandboxCertificate);
        ray($isVerified);

        //        if (! $isVerified) {
        //            return response()->json([
        //                'code' => '01',
        //                'message' => 'Invalid Signature',
        //                'details' => '',
        //                'refTransactionId' => $request->input('bankTransactionId'),
        //            ], Response::HTTP_BAD_REQUEST);
        //        }

        return response()->json([
            'code' => '00',
            'message' => 'Success',
            'details' => '',
            'refTransactionId' => $request->input('bankTransactionId'),
        ]);
    }
}
