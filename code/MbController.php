<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class MbController extends Controller
{
    protected static string $mbPublicKey = '-----B<PERSON>IN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjWvhEb/71O5JlcwCpFQn
dM51BxpKYpLes+iqxVTBHSSaaE77zwfYRzl6vVavUlllpi/XXp9zykcxTu5+PZz3
bM6qavB84XQZ0zMJ41gYBHAKoQLP3xy/gHta01TASTAX9gDfu9QYIlkS20fZS/oH
ZQ4Va1/bnoKujxSwYm7CMmWwgw8K9ER2PsV6JVeLi/hfHcI21vr3s32BU7MB3Vo+
LDuStpoPucu3AJi8CX1deyawqPxFu9NSSaxqKqbFGtIB7taXSfvN5YfKIQV/OB7C
WadujkHH+7by1+MR1nfRKbFDWxTaGbKwHxw7vzBvhK977QiguM+7ZvdVgTYQNhRE
LQIDAQAB
-----END PUBLIC KEY-----';

    protected static string $autoPayPublicKey = '-----BEGIN PUBLIC KEY-----
MIIBITANBgkqhkiG9w0BAQEFAAOCAQ4AMIIBCQKCAQB4TzASEZZE6y6EEjUz01Nd
f+n5lqaoPyYlnu2nKyOJdPIYPxPwSC2kShQR71FaomGH0aPT9s2oJqW4CgzfwBTH
29fAJJvmHYZL2JbAOKfi9ges67e41B2eU2kSSnwFaVYwFKvng+/gO0Mz9vJD4Zyy
eOzTZ/+Z6HTtwqPrZpsID6MHV1SVj/4hCf7KElATA71kwY3UQwMdz1AmTBefzBMf
rj+55yMEyxi4b/McqmW6LuLLioKBLG0X4bZRUZPFzfoY4fFyCfRIjb8ycz65UN9G
cq8Dj6m+mtjBZfCvZIHBztsVCi2zZ82HfkrMR7KkNqANdlNaSR5SCA/L7I9fzMAz
AgMBAAE=
-----END PUBLIC KEY-----';

    protected static string $autoPayPrivateKey = '******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

    public function tokenGenerate(Request $request): Response
    {
        ray('tokenGenerate', $request->header('authorization'), $request->all());

        $data = [
            'access_token' => config('autopay.mb.auth.token-generate'),
            'token_type' => 'bearer',
            'expires_in' => 59,
        ];

        return response()->json($data);
    }

    public function transactionSync(Request $request): Response
    {
        ray('transactionSync', $request->header('authorization'), $request->all(), json_encode($request->all()));

        $data = [
            'error' => false,
            'errorReason' => '000',
            'toastMessage' => '',
            'object' => [
                'reftransactionid' => $request->json('transactionid') ?? '',
            ],
        ];

        return response()->json($data);
    }

    public function vaAccount(Request $request): Response
    {
        $signatureBase64 = $request->header('signature');
        ray('signatureBase64', $signatureBase64);
        ray('headers', $request->headers->all());
        ray('requests', $request->all());

        $validated = (object) $request->validate([
            'customerAcc' => 'required|min:8|max:19',
        ]);

        //        $customerAcc = 'MBINTERDAUTOPAY2024';
        //        $prefix = 'INTERD';
        //        $customerAcc = 'MB'.$prefix.'AUTOPAY2024';

        $verify = openssl_verify($validated->customerAcc, base64_decode($signatureBase64), self::$mbPublicKey, OPENSSL_ALGO_SHA256);
        ray('isVerified', $verify);

        openssl_sign($validated->customerAcc, $verifySignature, self::$autoPayPrivateKey, OPENSSL_ALGO_SHA256);
        $verifySignature = base64_encode($verifySignature);
        ray('verifySignature', $verifySignature);

        if ($verify === 1) {
            $payload = [
                'requestId' => strtolower(Str::ulid()),
                'data' => [
                    'customerAcc' => $validated->customerAcc,
                    'customerName' => 'AUTOPAY',
                    'responseCode' => '00',
                    'responseDesc' => 'Thành công',
                ],
                'signature' => $verifySignature,
            ];

            return response()->json($payload);
        }

        $payload = [
            'requestId' => strtolower(Str::ulid()),
            'data' => [
                'customerAcc' => $validated->customerAcc,
                'customerName' => 'Không tìm thấy khách hàng',
                'responseDesc' => 'Không tìm thấy khách hàng',
                'responseCode' => '01',
            ],
            'signature' => $verifySignature,
        ];

        return response()->json($payload);
    }

    public function vaTransactionSync(Request $request): Response
    {
        ray('requests', $request->all());

        $validated = (object) $request->validate([
            'requestId' => 'required',
            'signature' => 'required',
            'data.referenceNumber' => 'required',
            'data.amount' => 'required',
            'data.customerAcc' => 'required|min:8|max:19',
            'data.transDate' => 'required',
            'data.billNumber' => 'required',
            'data.endPointUrl' => 'nullable',
            'data.userName' => 'nullable',
            'data.rate' => 'nullable',
            'data.customerName' => 'nullable',
            'data.additionalData.*.name' => 'nullable',
            'data.additionalData.*.value' => 'nullable',
        ]);

        $verify = openssl_verify($validated->data->referenceNumber.$validated->data->customerAcc.$validated->data->amount.$validated->data->transDate, base64_decode($validated->signature), self::$mbPublicKey, OPENSSL_ALGO_SHA256);
        ray('isVerified', $verify);

        $transactionId = strtolower(Str::ulid());

        if ($verify === 1) {
            $responseCode = '00';

            openssl_sign($transactionId.$responseCode, $verifySignature, self::$autoPayPrivateKey, OPENSSL_ALGO_SHA256);
            $verifySignature = base64_encode($verifySignature);

            $payload = [
                'requestId' => strtolower(Str::ulid()),
                'data' => [
                    'transactionId' => $transactionId,
                    'responseCode' => $responseCode,
                    'responseDesc' => 'Successful',
                ],
                'signature' => $verifySignature,
            ];

            return response()->json($payload);
        }

        $responseCode = '01';

        openssl_sign($transactionId.$responseCode, $verifySignature, self::$autoPayPrivateKey, OPENSSL_ALGO_SHA256);
        $verifySignature = base64_encode($verifySignature);

        $payload = [
            'requestId' => strtolower(Str::ulid()),
            'data' => [
                'transactionId' => $transactionId,
                'responseCode' => $responseCode,
                'responseDesc' => 'Failed',
            ],
        ];

        return response()->json($payload);
    }
}
